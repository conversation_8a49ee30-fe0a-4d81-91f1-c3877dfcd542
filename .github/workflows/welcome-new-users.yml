# Welcome new users by commenting on the first-time issue/PR.
# Adding a friendly message in the comment to tip non-stargazers to star our repo.
#
# Props to https://github.com/wow-actions/welcome 

name: Welcome New Users with Thanks and Tip non-stargazers ;-)
on:
  pull_request_target:
    types: [opened, closed]
  issues:
    types: [opened]
permissions:
  contents: read  # Set permissions as read-only by default at the top level
jobs:
  run:
    permissions:
      issues: write  # for wow-actions/welcome to comment on issues
      pull-requests: write  # wow-actions/welcome to comment on PRs
    # Do not run on bots and maintainers
    if: ${{ !contains(from<PERSON>son('["dependabot[bot]", "dependabot-preview[bot]", "allcontributors[bot]"]'), github.actor) }}
    runs-on: ubuntu-latest
    steps:
      - uses: wow-actions/welcome@68019c2c271561f63162fea75bb7707ef8a02c85 # To pin v1.3.1
        with:
          # GitHub token as default GITHUB_TOKEN
      
          FIRST_ISSUE_REACTIONS: '+1, hooray, rocket, heart'
          
          FIRST_ISSUE: |
            👋 @{{ author }}
            
            Welcome to the Microcks community! 💖
            
            Thanks and congrats 🎉 for opening your first issue here! Be sure to follow the issue template or please update it accordingly.
            
            📢 If you're using Microcks in your organization, please [add your company name to this list](https://github.com/microcks/.github/blob/main/ADOPTERS.md). 🙏 It really helps the project to gain momentum and credibility. It's a small contribution back to the project with a big impact.
            
            If you need to know why and how to add yourself to the list, please read the blog post ["Join the Microcks Adopters list and Empower the vibrant open source Community 🙌"](https://microcks.io/blog/join-adopters-list/)
            
            Hope you have a great time there! 
          
          FIRST_PR_REACTIONS: '+1, hooray, rocket, heart'
          
          FIRST_PR: |
            👋 @{{ author }}
            
            Welcome to the Microcks community! 💖
            
            Thanks and congrats 🎉 for opening your first pull request here! Be sure to follow the pull request template or please update it accordingly.
            
            Hope you have a great time there! 
          
          FIRST_PR_MERGED: |
            🎉 @{{ author }}
            
            You are now a Microcks community contributor! 💖
            
            Thanks and congrats 🚀 on merging your first pull request! We are delighted and very proud of you! 👏 
            
            📢 If you're using Microcks in your organization, please [add your company name to this list](https://github.com/microcks/.github/blob/main/ADOPTERS.md). 🙏 It really helps the project to gain momentum and credibility. It's a small contribution back to the project with a big impact.
            
            If you need to know why and how to add yourself to the list, please read the blog post ["Join the Microcks Adopters list and Empower the vibrant open source Community 🙌"](https://microcks.io/blog/join-adopters-list/)
            
            Kudos and please keep going, we need you 🙌
            
          STAR_MESSAGE: |
            🌟 ~~~~~~~~~ 🌟

            📢 If you like Microcks, please ⭐ star ⭐ our repo to support it!
            
            🙏 It really helps the project to gain momentum and credibility. It's a small contribution back to the project with a big impact.
            
