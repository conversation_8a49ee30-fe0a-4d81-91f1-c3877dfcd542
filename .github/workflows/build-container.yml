name: Build and Push Container Images
on:
  workflow_call:
    inputs:
      image-tag:
        description: 'The tag to use for the images'
        required: true
        type: string

jobs:
  build-containers:
    if: github.repository_owner == 'microcks' && inputs.image-tag != ''
    runs-on: ubuntu-latest
    environment: Build
    permissions:
      contents: read
      id-token: write # needed for signing the images with GitHub OIDC Token

    steps:
      - name: Get current date
        id: date
        #run: echo "::set-output name=date::$(date +'%Y-%m-%dT%H:%M:%S')"
        run: echo "date=$(date +'%Y-%m-%dT%H:%M:%SZ')" >> $GITHUB_OUTPUT

      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up JDK 21 for x64
        uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
        with:
          java-version: '21'
          distribution: 'temurin'
          architecture: x64
          cache: maven

      - name: <PERSON><PERSON>
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4.2.2
        with:
          path: ~/.m2/repository
          key: maven-repo-${{ github.run_id }}
          restore-keys: |
            maven-repo-

      - name: Package webapp in prod mode
        run: |
          # We should install the webapp to have the JAR with UI, available for the Uber distro
          mvn -B -q -Pprod -DskipTests install

      - name: Set IMAGE_TAG environment from input
        run: |
          set -x
          echo "IMAGE_TAG=${{ inputs.image-tag }}" >> "$GITHUB_ENV"

      - name: Install Cosign
        uses: sigstore/cosign-installer@d7d6bc7722e3daa8354c50bcb52f4837da5e9b6a # v3.8.1

      - name: Set up QEMU
        uses: docker/setup-qemu-action@29109295f81e9208d7d86ff1c6c12d2833863392 # v3.6.0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@b5ca514318bd6ebac0fb2aedd5d36ec1b5c232a2 # v3.10.0

      - name: Login to Quay.io and Docker Hub registries and setup multi-arch builder
        run: |
          echo ${{ secrets.QUAY_PASSWORD }} | docker login -u ${{ secrets.QUAY_USERNAME }} --password-stdin quay.io
          echo ${{ secrets.DOCKERHUB_TOKEN }} | docker login -u ${{ secrets.DOCKERHUB_USERNAME }} --password-stdin docker.io
          BUILDER=buildx-multi-arch
          docker buildx inspect $BUILDER || docker buildx create --name=$BUILDER --driver=docker-container --driver-opt=network=host

      - name: Build and push container image for webapp
        id: build-and-push-webapp
        uses: docker/build-push-action@471d1dc4e07e5cdedd4c2171150001c434f0b7a4 # v6.15.0
        with:
          context: ${{github.workspace}}/webapp
          sbom: true
          push: true
          provenance: mode=max
          platforms: linux/amd64,linux/arm64
          builder: buildx-multi-arch
          file: webapp/src/main/docker/Dockerfile
          labels: |
            org.opencontainers.image.revision=${GITHUB_SHA}
            org.opencontainers.image.created=${{ steps.date.outputs.date }}
          tags: quay.io/microcks/microcks:${{env.IMAGE_TAG}},docker.io/microcks/microcks:${{env.IMAGE_TAG}}

      - name: Sign the webapp images with GitHub OIDC Token
        env:
          DIGEST: ${{ steps.build-and-push-webapp.outputs.digest }}
          TAGS: quay.io/microcks/microcks:${{env.IMAGE_TAG}} docker.io/microcks/microcks:${{env.IMAGE_TAG}}
          COSIGN_EXPERIMENTAL: "true"
        run: |
          images=""
          for tag in ${TAGS}; do
            images+="${tag}@${DIGEST} "
          done
          cosign sign --yes ${images}

      - name: Package async minion in prod mode
        run: |
          cd ${{github.workspace}}/minions/async
          mvn -B -q -DskipTests -Dquarkus.package.type=legacy-jar package

      - name: Build and push container for async minion
        id: build-and-push-async
        uses: docker/build-push-action@471d1dc4e07e5cdedd4c2171150001c434f0b7a4 # v6.15.0
        with:
          context: ${{github.workspace}}/minions/async
          sbom: true
          push: true
          provenance: mode=max
          platforms: linux/amd64,linux/arm64
          builder: buildx-multi-arch
          file: minions/async/src/main/docker/Dockerfile.legacy-jar
          labels: |
            org.opencontainers.image.revision=${GITHUB_SHA}
            org.opencontainers.image.created=${{ steps.date.outputs.date }}
          tags: quay.io/microcks/microcks-async-minion:${{env.IMAGE_TAG}},docker.io/microcks/microcks-async-minion:${{env.IMAGE_TAG}}

      - name: Sign the async minion images with GitHub OIDC Token
        env:
          DIGEST: ${{ steps.build-and-push-async.outputs.digest }}
          TAGS: quay.io/microcks/microcks-async-minion:${{env.IMAGE_TAG}} docker.io/microcks/microcks-async-minion:${{env.IMAGE_TAG}}
          COSIGN_EXPERIMENTAL: "true"
        run: |
          images=""
          for tag in ${TAGS}; do
            images+="${tag}@${DIGEST} "
          done
          cosign sign --yes ${images}

      - name: Package uber distro in prod mode
        run: |
          cd ${{github.workspace}}/distro/uber
          mvn -B -q -Pprod -DskipTests package

      - name: Build and push container image for uber distro
        id: build-and-push-uber
        uses: docker/build-push-action@471d1dc4e07e5cdedd4c2171150001c434f0b7a4 # v6.15.0
        with:
          context: ${{github.workspace}}/distro/uber
          sbom: true
          push: true
          provenance: mode=max
          platforms: linux/amd64,linux/arm64
          builder: buildx-multi-arch
          file: distro/uber/src/main/docker/Dockerfile
          labels: |
            org.opencontainers.image.revision=${GITHUB_SHA}
            org.opencontainers.image.created=${{ steps.date.outputs.date }}
          tags: quay.io/microcks/microcks-uber:${{env.IMAGE_TAG}},docker.io/microcks/microcks-uber:${{env.IMAGE_TAG}}

      - name: Sign the uber distro images with GitHub OIDC Token
        env:
          DIGEST: ${{ steps.build-and-push-uber.outputs.digest }}
          TAGS: quay.io/microcks/microcks-uber:${{env.IMAGE_TAG}} docker.io/microcks/microcks-uber:${{env.IMAGE_TAG}}
          COSIGN_EXPERIMENTAL: "true"
        run: |
          images=""
          for tag in ${TAGS}; do
            images+="${tag}@${DIGEST} "
          done
          cosign sign --yes ${images}

      - name: Package async minion in uber distro in prod mode
        run: |
          cd ${{github.workspace}}/distro/uber-async-minion
          mvn -B -q -DskipTests package

      - name: Build and push container for async minion in uber distro
        id: build-and-push-uber-async
        uses: docker/build-push-action@471d1dc4e07e5cdedd4c2171150001c434f0b7a4 # v6.15.0
        with:
          context: ${{github.workspace}}/distro/uber-async-minion
          sbom: true
          push: true
          provenance: mode=max
          platforms: linux/amd64,linux/arm64
          builder: buildx-multi-arch
          file: distro/uber-async-minion/src/main/docker/Dockerfile.jvm
          labels: |
            org.opencontainers.image.revision=${GITHUB_SHA}
            org.opencontainers.image.created=${{ steps.date.outputs.date }}
          tags: quay.io/microcks/microcks-uber-async-minion:${{env.IMAGE_TAG}},docker.io/microcks/microcks-uber-async-minion:${{env.IMAGE_TAG}}

      - name: Sign the async minion in uber distro images with GitHub OIDC Token
        env:
          DIGEST: ${{ steps.build-and-push-uber-async.outputs.digest }}
          TAGS: quay.io/microcks/microcks-uber-async-minion:${{env.IMAGE_TAG}} docker.io/microcks/microcks-uber-async-minion:${{env.IMAGE_TAG}}
          COSIGN_EXPERIMENTAL: "true"
        run: |
          images=""
          for tag in ${TAGS}; do
            images+="${tag}@${DIGEST} "
          done
          cosign sign --yes ${images}
