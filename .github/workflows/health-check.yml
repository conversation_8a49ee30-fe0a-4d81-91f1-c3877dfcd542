name: health-check-tests
on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to check'
        required: true
  schedule:
    - cron: '0 4 * * *'  # Everyday at 4am UTC
permissions: read-all

jobs:
  check-compose:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    strategy:
      fail-fast: false
      matrix:
        include:
          # docker-compose config
          - method: docker
            mode: default
            addons: ""
          - method: docker
            mode: default
            addons: "async"
          - method: docker
            mode: devmode
            addons: ""
          # podman-compose config
          - method: podman
            mode: default
            addons: ""
          - method: podman
            mode: default
            addons: "async"
          - method: podman
            mode: devmode
            addons: ""

    steps:
      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
          ref: ${{ github.event.inputs.branch || '1.13.x' }}

      - name: Install docker-compose
        if: matrix.method == 'docker'
        run: |
          sudo apt-get update && sudo apt-get install -y docker-compose

      - name: Install podman and podman-compose
        if: matrix.method == 'podman'
        run: |
          sudo apt-get update && sudo apt-get install -y podman python3-pip
          pip3 install --user podman-compose
          echo "$HOME/.local/bin" >> $GITHUB_PATH

      - name: Install Microcks (${{ matrix.method }}/${{ matrix.mode }})
        run: |
          ARGS=(--method "${{ matrix.method }}" --mode "${{ matrix.mode }}")
          [[ -n "${{ matrix.addons }}" ]] && ARGS+=(--addons "${{ matrix.addons }}")
          ./testsuite/install-compose.sh "${ARGS[@]}"

      - name: Health-check containers
        run: |
          # Wait a bit for everything to be really up
          sleep 25
          # We're already cd'ed into install/<method>-compose by the installer
          ./testsuite/check-health.sh --method "${{ matrix.method }}"

      - name: Tear down
        if: always()
        run: |
          cd install/${{ matrix.method }}-compose
          ${{ matrix.method }}-compose down -v --remove-orphans

  check-helm:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    strategy:
      matrix:
        async: [true, false]
      fail-fast: false

    steps:
      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
          ref: ${{ github.event.inputs.branch || '1.13.x' }}

      - name: Set up Minikube
        uses: medyagh/setup-minikube@e3c7f79eb1e997eabccc536a6cf318a2b0fe19d9 # v0.0.14

      - name: Start Minikube
        run: |
          minikube start --driver=docker

      - name: Install Helm
        uses: azure/setup-helm@5119fcb9089d432beecbf79bb2c7915207344b78 # v3.5
        with:
          version: v3.13.1

      - name: Run install-helm.sh script
        run: |
          if [[ "${{ matrix.async }}" == "true" ]]; then
            ./testsuite/install-helm.sh --async
          else
            ./testsuite/install-helm.sh
          fi

      - name: Health-check containers
        run: |
          # Wait a bit for everything to be really up
          sleep 25
          # We're already cd'ed into install/<method>-compose by the installer
          ./testsuite/check-health.sh --method helm

      - name: Dump logs on failure
        if: failure()
        run: |
          kubectl get pods -A
          kubectl describe pods -n microcks || true
          kubectl logs -n microcks -l app=microcks --tail=100 || true

      - name: Tear Down
        if: always()
        run: |
          helm uninstall microcks -n microcks || true
          helm uninstall strimzi -n microcks || true
          kubectl delete namespace microcks --ignore-not-found=true
          minikube delete || true
