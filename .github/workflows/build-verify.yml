name: build-verify-package
on:
  push:
    paths-ignore:
      #- '.github/**'
      - '.gitignore'
      - 'LICENSE'
      - '*.md'
  pull_request:
    paths-ignore:
      - '.github/**'
      - '.gitignore'
      - 'LICENSE'
      - '*.md'
permissions: read-all

jobs:
  build-install:
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up JDK 21 for x64
        uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
        with:
          java-version: '21'
          distribution: 'temurin'
          architecture: x64
          cache: maven

      - name: Build Java components
        run: mvn -B clean install

      - name: Cache Maven dependencies
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4.2.2
        with:
          path: ~/.m2/repository
          key: maven-repo-${{ github.run_id }}
          restore-keys: |
            maven-repo-

  package-webapp:
    runs-on: ubuntu-latest
    needs: build-install
    permissions:
      contents: read
    steps:
      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up JDK 21 for x64
        uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
        with:
          java-version: '21'
          distribution: 'temurin'
          architecture: x64
          cache: maven

      - name: Restore Maven Cache
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4.2.2
        with:
          path: ~/.m2/repository
          key: maven-repo-${{ github.run_id }}
          restore-keys: |
            maven-repo-

      - name: Build Angular app
        run: |
          mvn -B -DskipTests install
          cd ${{github.workspace}}/webapp
          mvn -B -Pprod -DskipTests install
          
      - id: set-environment
        name: Set environment for branch
        run: |
          set -x
          if [[ $GITHUB_REF == 'refs/heads/master' ]]; then
            echo "IMAGE_TAG=latest" >> "$GITHUB_ENV"
            echo "IMAGE_TAG=latest" >> "$GITHUB_OUTPUT"
            echo "PACKAGE_IMAGE=true" >> "$GITHUB_ENV"
            echo "PACKAGE_IMAGE=true" >> "$GITHUB_OUTPUT"
          elif [[ $GITHUB_REF == 'refs/heads/1.11.x' ]]; then
            echo "IMAGE_TAG=maintenance" >> "$GITHUB_ENV"
            echo "IMAGE_TAG=maintenance" >> "$GITHUB_OUTPUT"
            echo "PACKAGE_IMAGE=true" >> "$GITHUB_ENV"
            echo "PACKAGE_IMAGE=true" >> "$GITHUB_OUTPUT"
          elif [[ $GITHUB_REF == 'refs/heads/1.12.x' ]]; then
            echo "IMAGE_TAG=nightly" >> "$GITHUB_ENV"
            echo "IMAGE_TAG=nightly" >> "$GITHUB_OUTPUT"
            echo "PACKAGE_IMAGE=true" >> "$GITHUB_ENV"
            echo "PACKAGE_IMAGE=true" >> "$GITHUB_OUTPUT"
          elif [[ $GITHUB_REF == "refs/tags/$GITHUB_REF_NAME" ]]; then
            echo "IMAGE_TAG=$GITHUB_REF_NAME" >> "$GITHUB_ENV"
            echo "IMAGE_TAG=$GITHUB_REF_NAME" >> "$GITHUB_OUTPUT"
            echo "PACKAGE_IMAGE=true" >> "$GITHUB_ENV"
            echo "PACKAGE_IMAGE=true" >> "$GITHUB_OUTPUT"
          else
            echo "PACKAGE_IMAGE=false" >> "$GITHUB_ENV"
            echo "PACKAGE_IMAGE=false" >> "$GITHUB_OUTPUT"
          fi

    outputs:
      image-tag: ${{ steps.set-environment.outputs.IMAGE_TAG }}
      package-image: ${{ steps.set-environment.outputs.PACKAGE_IMAGE }}

  integration-tests:
    runs-on: ubuntu-latest
    needs: build-install
    permissions:
      contents: read
    steps:
      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up JDK 21 for x64
        uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
        with:
          java-version: '21'
          distribution: 'temurin'
          architecture: x64
          cache: maven

      - name: Restore Maven Cache
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4.2.2
        with:
          path: ~/.m2/repository
          key: maven-repo-${{ github.run_id }}
          restore-keys: |
            maven-repo-

      - name: Run Integration tests
        run: |
          mvn -B -DskipTests install
          cd ${{github.workspace}}/webapp
          mvn -Pit test
          cd ${{github.workspace}}/minions/async
          mvn -Pit test

  fuzzing-tests:
    runs-on: ubuntu-latest
    needs: build-install
    permissions:
      contents: read
    steps:
      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up JDK 21 for x64
        uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
        with:
          java-version: '21'
          distribution: 'temurin'
          architecture: x64
          cache: maven

      - name: Restore Maven Cache
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4.2.2
        with:
          path: ~/.m2/repository
          key: maven-repo-${{ github.run_id }}
          restore-keys: |
            maven-repo-

      - name: Run Fuzzing tests
        run: |
          mvn -B -DskipTests install
          cd ${{github.workspace}}/webapp
          mvn -Pfuzz test

  javadoc-check:
    runs-on: ubuntu-latest
    needs: build-install
    permissions:
      contents: read
    steps:
      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up JDK 21 for x64
        uses: actions/setup-java@3a4f6e1af504cf6a31855fa899c6aa5355ba6c12 # v4.7.0
        with:
          java-version: '21'
          distribution: 'temurin'
          architecture: x64
          cache: maven

      - name: Restore Maven Cache
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4.2.2
        with:
          path: ~/.m2/repository
          key: maven-repo-${{ github.run_id }}
          restore-keys: |
            maven-repo-

      - name: Verify Javadoc completion
        run: |
          mvn -B -DskipTests install
          mvn -B javadoc:javadoc

  container-images-build:
    needs:
      - package-webapp
      - integration-tests
      - fuzzing-tests
      - javadoc-check
    if: github.repository_owner == 'microcks' && needs.package-webapp.outputs.package-image == 'true' && github.event_name != 'pull_request'
    permissions:
      contents: read
      id-token: write # needed for signing the images with GitHub OIDC Token
    uses: ./.github/workflows/build-container.yml
    with:
      image-tag: ${{ needs.package-webapp.outputs.image-tag }}
    secrets: inherit

  call-package-native:
    needs:
      - package-webapp
      - integration-tests
      - fuzzing-tests
      - javadoc-check
    if: github.repository_owner == 'microcks' && needs.package-webapp.outputs.package-image == 'true' && github.event_name != 'pull_request'
    permissions:
      contents: read
      id-token: write # needed for signing the images with GitHub OIDC Token
    uses: ./.github/workflows/package-native.yml
    with:
      image-tag: ${{ needs.package-webapp.outputs.image-tag }}
    secrets: inherit
