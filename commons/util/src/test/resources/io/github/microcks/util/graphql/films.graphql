# microcksId: Movie Graph API : 1.0
schema {
    query: Query
    mutation: Mutation
}
type Film {
    id: String!
    title: String!
    episodeID: Int!
    director: String!
    starCount: Int!
    rating: Float!
}

type FilmsConnection {
    totalCount: Int!
    films: [Film]
}

input Review {
    comment: String
    rating: Int
}

type Query {
    allFilms: FilmsConnection
    film(id: String): Film
}

type Mutation {
    addStar(filmId: String): Film
    addReview(filmId: String, review: Review): Film
}

