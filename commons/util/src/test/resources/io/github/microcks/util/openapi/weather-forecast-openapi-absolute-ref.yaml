---
openapi: 3.0.2
info:
  title: WeatherForecast API
  version: 1.0.0
  description: A simple API for demonstrating dispatching capabilities in Microcks
  contact:
    name: <PERSON>
    url: https://github.com/lbroudoux
    email: <EMAIL>
  license:
    name: MIT License
    url: https://opensource.org/licenses/MIT
paths:
  /forecast/{region}:
    get:
      operationId: GetForecast
      summary: Get forecast for region
      parameters:
        - name: region
          description: The region to get forecast for
          schema:
            type: string
          in: path
          required: true
          examples:
            unknown:
              value: other
            north:
              value: north
            west:
              value: west
            east:
              value: east
            south:
              value: south
        - name: apiKey
          description: Client API key
          schema:
            type: string
          in: query
          required: true
      responses:
        "200":
          description: Weather forecast for region
          content:
            application/json:
              schema:
                $ref: 'https://raw.githubusercontent.com/microcks/microcks/1.5.x/webapp/src/test/resources/io/github/microcks/util/openapi/weather-forecast-schema.json'
              examples:
                north:
                  value:
                    region: north
                    temp: -1.5
                    weather: snowy
                    visibility: 25
                west:
                  value:
                    region: west
                    temp: 12.2
                    weather: rainy
                    visibility: 300
                east:
                  value:
                    region: east
                    temp: -6.6
                    weather: frosty
                    visibility: 523
                south:
                  value:
                    region: south
                    temp: 28.3
                    weather: sunny
                    visibility: 1500
        "404":
          description: Region is unknown
          content:
            application/json:
              schema:
                type: string
              examples:
                unknown:
                  value: "Region is unknown. Choose in north, west, east or south."