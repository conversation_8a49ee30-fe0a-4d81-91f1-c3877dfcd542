<?xml version='1.0' encoding='UTF-8'?>
<wsdl:definitions xmlns:xsd="http://www.w3.org/2001/XMLSchema"
	xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://www.example.com/hello"
	xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:ns1="http://schemas.xmlsoap.org/soap/http"
	name="HelloService" targetNamespace="http://www.example.com/hello">
	<wsdl:types>
		<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
			xmlns:tns="http://www.example.com/hello" attributeFormDefault="unqualified"
			elementFormDefault="unqualified" targetNamespace="http://www.example.com/hello">
			<xs:element name="sayHello" type="tns:sayHello" />
			<xs:element name="sayHelloResponse" type="tns:sayHelloResponse" />
			<xs:complexType name="sayHello">
				<xs:sequence>
					<xs:element minOccurs="0" name="name" type="xs:string" />
				</xs:sequence>
			</xs:complexType>
			<xs:complexType name="sayHelloResponse">
				<xs:sequence>
					<xs:element minOccurs="0" name="sayHello" type="xs:string" />
				</xs:sequence>
			</xs:complexType>
			<xs:element name="HelloException" type="tns:HelloException" />
			<xs:complexType name="HelloException">
				<xs:sequence>
					<xs:element name="code" nillable="true" type="xs:string" />
				</xs:sequence>
			</xs:complexType>
		</xs:schema>
	</wsdl:types>
	<wsdl:message name="HelloException">
		<wsdl:part element="tns:HelloException" name="HelloException" />
	</wsdl:message>
	<wsdl:message name="sayHelloResponse">
		<wsdl:part element="tns:sayHelloResponse" name="parameters" />
	</wsdl:message>
	<wsdl:message name="sayHello">
		<wsdl:part element="tns:sayHello" name="parameters" />
	</wsdl:message>
	<wsdl:portType name="HelloService">
		<wsdl:operation name="sayHello">
			<wsdl:input message="tns:sayHello" name="sayHello" />
			<wsdl:output message="tns:sayHelloResponse" name="sayHelloResponse" />
			<wsdl:fault message="tns:HelloException" name="HelloException" />
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="HelloServiceSoapBinding" type="tns:HelloService">
		<soap:binding style="document"
			transport="http://schemas.xmlsoap.org/soap/http" />
		<wsdl:operation name="sayHello">
			<soap:operation soapAction="" style="document" />
			<wsdl:input name="sayHello">
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output name="sayHelloResponse">
				<soap:body use="literal" />
			</wsdl:output>
			<wsdl:fault name="HelloException">
				<soap:fault name="HelloException" use="literal" />
			</wsdl:fault>
		</wsdl:operation>
	</wsdl:binding>
	<wsdl:service name="HelloService">
		<wsdl:port binding="tns:HelloServiceSoapBinding" name="HelloServiceEndpointPort">
			<soap:address location="http://localhost:8080/services/HelloService" />
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>
