asyncapi: '2.1.0'
info:
  title: Account Service
  version: 1.2.0
  description: This service is in charge of processing user signups
channels:
  user/signedup:
    subscribe:
      message:
        $ref: '#/components/messages/UserSignedUp'
components:
  schemas:
    size:
      type: number
      format: float
    exp:
      type: object
      properties:
        level:
          type: integer
          format: int64
    UserSignedUpType:
      type: object
      properties:
        displayName:
          type: string
        age:
          type: integer
          format: int32
        size:
          $ref: '#/components/schemas/size'
        exp:
          $ref: '#/components/schemas/exp'
        rewards:
          type: number
          format: double
      required:
        - displayName
      additionalProperties: false
  messages:
    UserSignedUp:
      payload:
        oneOf:
          - $ref: '#/components/schemas/UserSignedUpType'
      examples:
        - name: Laurent
          payload:
            displayName: Laurent Broudoux
            age: 43
            size: 1.8
            exp: 1234567891011
            rewards: 12345.67