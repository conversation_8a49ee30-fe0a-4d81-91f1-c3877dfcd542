asyncapi: '2.1.0'
info:
  title: Account Service
  version: 1.2.0
  description: This service is in charge of processing user signups
channels:
  user/signedup:
    subscribe:
      message:
        $ref: '#/components/messages/UserSignedUp'
components:
  messages:
    UserSignedUp:
      payload:
        type: object
        properties:
          displayName:
            type: string
          age:
            type: integer
            format: int32
          size:
            $ref: '#/components/schemas/size'
          exp:
            $ref: '#/components/schemas/exp'
          rewards:
            type: number
            format: double
        required:
          - displayName
        additionalProperties: false
      examples:
        - name: Laurent
          payload:
            displayName: <PERSON>
            age: 43
            size: 1.8
            exp: *************
            rewards: 12345.67
  schemas:
    size:
      type: number
      format: float
    exp:
      type: object
      properties:
        level:
          type: integer
          format: int64