<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="note">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="to" type="xs:string"/>
        <xs:element name="from" type="xs:string"/>
        <xs:element name="heading" type="xs:string"/>
        <!-- Missing closing tag for body element -->
        <xs:element name="body" type="xs:string">
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>
