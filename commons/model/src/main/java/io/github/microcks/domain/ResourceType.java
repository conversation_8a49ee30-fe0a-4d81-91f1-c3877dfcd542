/*
 * Copyright The Microcks Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package io.github.microcks.domain;

/**
 * Enumeration of types of resources managed by microservices.
 * <AUTHOR>
 */
public enum ResourceType {
   WSDL,
   XSD,
   JSON_SCHEMA,
   SWAGGER,
   RAML,
   OPEN_API_SPEC,
   OPEN_API_SCHEMA,
   ASYNC_API_SPEC,
   ASYNC_API_SCHEMA,
   AVRO_SCHEMA,
   PROTOBUF_SCHEMA,
   PROTOBUF_DESCRIPTOR,
   GRAPHQL_SCHEMA,
   POSTMAN_COLLECTION,
   SOAP_UI_PROJECT,
   JSON_FRAGMENT
}
