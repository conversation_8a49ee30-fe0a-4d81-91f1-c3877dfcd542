/*
 * Copyright The Microcks Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package io.github.microcks.domain;

/**
 * A lightweight reference to a Secret object.
 * <AUTHOR>
 */
public class SecretRef {

   private String secretId;
   private String name;

   public SecretRef() {
   }

   public SecretRef(String secretId, String name) {
      this.secretId = secretId;
      this.name = name;
   }

   public String getSecretId() {
      return secretId;
   }

   public void setSecretId(String secretId) {
      this.secretId = secretId;
   }

   public String getName() {
      return name;
   }

   public void setName(String name) {
      this.name = name;
   }
}
