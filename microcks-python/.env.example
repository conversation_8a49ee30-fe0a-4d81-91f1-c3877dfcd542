# Microcks Python Configuration
# Copy this file to .env and modify the values as needed

# =============================================================================
# Server Configuration
# =============================================================================
HOST=0.0.0.0
PORT=8080
DEBUG=true
RELOAD=true

# =============================================================================
# Database Configuration
# =============================================================================
# MongoDB connection URL
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=microcks
MONGODB_MIN_POOL_SIZE=10
MONGODB_MAX_POOL_SIZE=100

# Redis connection URL (optional, for caching)
REDIS_URL=redis://localhost:6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_SSL=false

# =============================================================================
# Authentication & Security
# =============================================================================
# JWT configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Password hashing
PASSWORD_HASH_ALGORITHM=bcrypt
PASSWORD_HASH_ROUNDS=12

# CORS configuration
ENABLE_CORS=true
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
CORS_ALLOWED_HEADERS=*
CORS_ALLOW_CREDENTIALS=true

# =============================================================================
# Features Configuration
# =============================================================================
# Async API support
ENABLE_ASYNC_API=true
DEFAULT_ASYNC_BINDING=KAFKA
DEFAULT_ASYNC_FREQUENCY=3

# AI Copilot features
ENABLE_AI_COPILOT=false
AI_COPILOT_PROVIDER=openai
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=3000
OPENAI_TIMEOUT=30

# Dynamic mocking
ENABLE_DYNAMIC_MOCKING=true
DYNAMIC_MOCK_TTL=3600

# Import/Export features
ENABLE_IMPORT_EXPORT=true
MAX_UPLOAD_SIZE=10485760  # 10MB
ALLOWED_UPLOAD_EXTENSIONS=.json,.yaml,.yml,.xml,.proto,.graphql,.har

# =============================================================================
# Protocol Support
# =============================================================================
# REST API mocking
ENABLE_REST_MOCKING=true
REST_MOCK_ENABLE_VALIDATION=true
REST_MOCK_ENABLE_CORS=true

# GraphQL support
ENABLE_GRAPHQL=true
GRAPHQL_INTROSPECTION=true
GRAPHQL_PLAYGROUND=true

# gRPC support
ENABLE_GRPC=true
GRPC_PORT=9090
GRPC_REFLECTION=true

# WebSocket support
ENABLE_WEBSOCKET=true
WEBSOCKET_PING_INTERVAL=20
WEBSOCKET_PING_TIMEOUT=10

# =============================================================================
# Async Messaging Configuration
# =============================================================================
# Kafka configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_SECURITY_PROTOCOL=PLAINTEXT
KAFKA_SASL_MECHANISM=
KAFKA_SASL_USERNAME=
KAFKA_SASL_PASSWORD=

# MQTT configuration
MQTT_BROKER_HOST=localhost
MQTT_BROKER_PORT=1883
MQTT_USERNAME=
MQTT_PASSWORD=
MQTT_USE_TLS=false

# NATS configuration
NATS_SERVERS=nats://localhost:4222
NATS_USERNAME=
NATS_PASSWORD=
NATS_TOKEN=

# RabbitMQ/AMQP configuration
AMQP_URL=amqp://guest:guest@localhost:5672/
AMQP_EXCHANGE=microcks
AMQP_QUEUE_PREFIX=microcks

# =============================================================================
# Monitoring & Observability
# =============================================================================
# Metrics
ENABLE_METRICS=true
METRICS_PATH=/metrics
PROMETHEUS_MULTIPROC_DIR=/tmp/prometheus_multiproc

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json  # json or text
LOG_FILE=
LOG_ROTATION=true
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5

# OpenTelemetry tracing
ENABLE_TRACING=false
OTEL_SERVICE_NAME=microcks-python
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317
OTEL_EXPORTER_OTLP_HEADERS=
OTEL_RESOURCE_ATTRIBUTES=service.name=microcks-python,service.version=0.1.0

# Health checks
HEALTH_CHECK_TIMEOUT=30
HEALTH_CHECK_INTERVAL=60

# =============================================================================
# Performance & Scaling
# =============================================================================
# Request handling
MAX_REQUEST_SIZE=10485760  # 10MB
REQUEST_TIMEOUT=30
RESPONSE_TIMEOUT=30

# Connection pooling
HTTP_POOL_CONNECTIONS=100
HTTP_POOL_MAXSIZE=100
HTTP_MAX_RETRIES=3

# Caching
ENABLE_RESPONSE_CACHING=true
CACHE_TTL=300  # 5 minutes
CACHE_MAX_SIZE=1000

# Rate limiting
ENABLE_RATE_LIMITING=false
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=3600  # 1 hour

# =============================================================================
# Development & Testing
# =============================================================================
# Development mode settings
ENABLE_DEBUG_TOOLBAR=false
ENABLE_PROFILING=false
PROFILE_OUTPUT_DIR=/tmp/profiles

# Testing
TEST_DATABASE_URL=mongodb://localhost:27017/microcks_test
TEST_REDIS_URL=redis://localhost:6379/1

# Mock data generation
ENABLE_MOCK_DATA_GENERATION=true
MOCK_DATA_FAKER_LOCALE=en_US
MOCK_DATA_SEED=42

# =============================================================================
# External Integrations
# =============================================================================
# Webhook notifications
ENABLE_WEBHOOKS=false
WEBHOOK_TIMEOUT=10
WEBHOOK_RETRY_COUNT=3

# Email notifications
ENABLE_EMAIL_NOTIFICATIONS=false
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_USE_TLS=true
EMAIL_FROM=<EMAIL>

# Slack notifications
ENABLE_SLACK_NOTIFICATIONS=false
SLACK_WEBHOOK_URL=
SLACK_CHANNEL=#microcks

# =============================================================================
# Security Headers
# =============================================================================
SECURITY_HEADERS_ENABLED=true
HSTS_MAX_AGE=31536000
CONTENT_TYPE_NOSNIFF=true
X_FRAME_OPTIONS=DENY
X_XSS_PROTECTION=1; mode=block
REFERRER_POLICY=strict-origin-when-cross-origin
