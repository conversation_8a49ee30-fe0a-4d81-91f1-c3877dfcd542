# Microcks Python

A Python/FastAPI implementation of Microcks - the Kubernetes-native tool for API mocking and testing.

## Overview

Microcks Python is a modern, high-performance clone of the original [Microcks](https://microcks.io) project, built with Python and FastAPI. It provides comprehensive API mocking and testing capabilities for REST, GraphQL, gRPC, AsyncAPI, and other protocols.

## Features

### 🚀 Core Capabilities
- **API Mocking**: Turn API specifications into live mocks in seconds
- **API Testing**: Run compliance and regression tests against API implementations
- **Multi-Protocol Support**: REST, GraphQL, gRPC, AsyncAPI, SOAP, WebSocket
- **Specification Import**: OpenAPI, AsyncAPI, GraphQL schemas, gRPC protobuf, Postman collections

### 🔧 Advanced Features
- **Dynamic Mocking**: Generate mock responses based on API specifications
- **Stateful Interactions**: Support for stateful mocking and data persistence
- **AI-Powered Generation**: Automatic mock and test generation using AI
- **Async Messaging**: Support for Kafka, MQTT, WebSocket, AMQP, NATS
- **CI/CD Integration**: Command-line tools for automated testing

### 📊 Monitoring & Analytics
- **Invocation Statistics**: Track API mock usage and performance
- **Test Conformance**: Monitor API compliance and test success rates
- **Prometheus Metrics**: Export metrics for monitoring systems
- **OpenTelemetry**: Distributed tracing support

## Quick Start

### Prerequisites
- Python 3.11 or higher
- MongoDB 4.4 or higher
- Redis (optional, for caching)

### Installation

```bash
# Clone the repository
git clone https://github.com/microcks/microcks-python.git
cd microcks-python

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -e .

# Install development dependencies
pip install -e ".[dev]"
```

### Configuration

Create a `.env` file in the project root:

```env
# Database Configuration
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=microcks

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379

# Authentication
JWT_SECRET_KEY=your-secret-key-here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Server Configuration
HOST=0.0.0.0
PORT=8080
DEBUG=true

# Features
ENABLE_ASYNC_API=true
ENABLE_AI_COPILOT=false
OPENAI_API_KEY=your-openai-key-here
```

### Running the Server

```bash
# Development mode
uvicorn microcks.main:app --reload --host 0.0.0.0 --port 8080

# Or use the CLI
microcks-server --host 0.0.0.0 --port 8080 --reload
```

### Using Docker

```bash
# Build the image
docker build -t microcks-python .

# Run with Docker Compose
docker-compose up -d
```

## API Documentation

Once the server is running, you can access:

- **API Documentation**: http://localhost:8080/docs (Swagger UI)
- **Alternative Docs**: http://localhost:8080/redoc (ReDoc)
- **Health Check**: http://localhost:8080/health

## Usage Examples

### Import an OpenAPI Specification

```bash
# Using the CLI
microcks import --type openapi --file petstore.yaml

# Using the API
curl -X POST "http://localhost:8080/api/artifacts" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@petstore.yaml" \
  -F "mainArtifact=true"
```

### Create a Test

```bash
# Using the CLI
microcks test --service "Petstore API" --version "1.0.0" \
  --endpoint "http://api.example.com" --runner OPEN_API_SCHEMA

# Using the API
curl -X POST "http://localhost:8080/api/tests" \
  -H "Content-Type: application/json" \
  -d '{
    "serviceId": "Petstore API:1.0.0",
    "testEndpoint": "http://api.example.com",
    "runnerType": "OPEN_API_SCHEMA"
  }'
```

### Access Mock Endpoints

```bash
# REST API mock
curl http://localhost:8080/rest/Petstore%20API/1.0.0/pets

# GraphQL mock
curl -X POST http://localhost:8080/graphql/MyAPI/1.0.0 \
  -H "Content-Type: application/json" \
  -d '{"query": "{ pets { id name } }"}'
```

## Development

### Project Structure

```
microcks-python/
├── microcks/                 # Main application package
│   ├── api/                  # API route handlers
│   ├── core/                 # Core business logic
│   ├── models/               # Pydantic models
│   ├── services/             # Business services
│   ├── repositories/         # Data access layer
│   ├── utils/                # Utility functions
│   ├── cli/                  # Command-line interface
│   └── main.py               # Application entry point
├── tests/                    # Test suite
├── docs/                     # Documentation
├── docker/                   # Docker configuration
├── scripts/                  # Utility scripts
└── examples/                 # Example configurations
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=microcks --cov-report=html

# Run specific test categories
pytest -m unit          # Unit tests only
pytest -m integration   # Integration tests only
pytest -m "not slow"    # Exclude slow tests
```

### Code Quality

```bash
# Format code
black microcks tests

# Sort imports
isort microcks tests

# Lint code
flake8 microcks tests

# Type checking
mypy microcks

# Run all quality checks
pre-commit run --all-files
```

## Deployment

### Docker

```dockerfile
# Multi-stage build for production
FROM python:3.11-slim as builder
WORKDIR /app
COPY pyproject.toml .
RUN pip install build && python -m build

FROM python:3.11-slim
WORKDIR /app
COPY --from=builder /app/dist/*.whl .
RUN pip install *.whl
EXPOSE 8080
CMD ["microcks-server", "--host", "0.0.0.0", "--port", "8080"]
```

### Kubernetes

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: microcks-python
spec:
  replicas: 3
  selector:
    matchLabels:
      app: microcks-python
  template:
    metadata:
      labels:
        app: microcks-python
    spec:
      containers:
      - name: microcks-python
        image: microcks/microcks-python:latest
        ports:
        - containerPort: 8080
        env:
        - name: MONGODB_URL
          value: "mongodb://mongodb:27017"
        - name: REDIS_URL
          value: "redis://redis:6379"
```

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Original [Microcks](https://microcks.io) project and community
- [FastAPI](https://fastapi.tiangolo.com/) framework
- [Pydantic](https://pydantic-docs.helpmanual.io/) for data validation
- All the amazing Python open-source libraries that make this possible

## Support

- 📖 [Documentation](https://microcks-python.readthedocs.io)
- 🐛 [Issue Tracker](https://github.com/microcks/microcks-python/issues)
- 💬 [Discussions](https://github.com/microcks/microcks-python/discussions)
- 📧 [Email Support](mailto:<EMAIL>)
