[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "microcks-python"
version = "0.1.0"
description = "Python/FastAPI clone of Microcks - API mocking and testing platform"
readme = "README.md"
license = "Apache-2.0"
authors = [
    { name = "Microcks Python Team", email = "<EMAIL>" }
]
maintainers = [
    { name = "Microcks Python Team", email = "<EMAIL>" }
]
keywords = [
    "api",
    "mocking",
    "testing",
    "openapi",
    "asyncapi",
    "graphql",
    "grpc",
    "microservices"
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Testing",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Libraries :: Application Frameworks"
]
requires-python = ">=3.11"
dependencies = [
    # Core FastAPI and async support
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    
    # Database and storage
    "motor>=3.3.0",  # Async MongoDB driver
    "redis>=5.0.0",  # Redis for caching
    "pymongo>=4.6.0",  # MongoDB sync driver for migrations
    
    # HTTP client and networking
    "httpx>=0.25.0",  # Async HTTP client
    "websockets>=12.0",  # WebSocket support
    
    # Authentication and security
    "python-jose[cryptography]>=3.3.0",  # JWT handling
    "passlib[bcrypt]>=1.7.4",  # Password hashing
    "python-multipart>=0.0.6",  # Form data parsing
    
    # API specification parsing
    "pyyaml>=6.0.1",  # YAML parsing
    "jsonschema>=4.20.0",  # JSON schema validation
    "openapi-spec-validator>=0.7.1",  # OpenAPI validation
    "jsonref>=1.1.0",  # JSON reference resolution
    
    # Template engine and rendering
    "jinja2>=3.1.2",  # Template engine
    "markupsafe>=2.1.3",  # Safe string handling
    
    # Async messaging and protocols
    "aiokafka>=0.10.0",  # Kafka async client
    "aio-pika>=9.3.0",  # RabbitMQ/AMQP async client
    "asyncio-mqtt>=0.16.0",  # MQTT async client
    "nats-py>=2.6.0",  # NATS async client
    
    # gRPC support
    "grpcio>=1.60.0",  # gRPC core
    "grpcio-tools>=1.60.0",  # gRPC tools
    "protobuf>=4.25.0",  # Protocol buffers
    
    # GraphQL support
    "graphql-core>=3.2.3",  # GraphQL core
    "strawberry-graphql[fastapi]>=0.215.0",  # GraphQL framework
    
    # Utilities and helpers
    "python-dateutil>=2.8.2",  # Date utilities
    "croniter>=2.0.1",  # Cron expression parsing
    "click>=8.1.7",  # CLI framework
    "rich>=13.7.0",  # Rich text and progress bars
    "typer>=0.9.0",  # CLI framework built on Click
    
    # Monitoring and observability
    "prometheus-client>=0.19.0",  # Prometheus metrics
    "opentelemetry-api>=1.21.0",  # OpenTelemetry tracing
    "opentelemetry-sdk>=1.21.0",  # OpenTelemetry SDK
    "opentelemetry-instrumentation-fastapi>=0.42b0",  # FastAPI instrumentation
    "opentelemetry-instrumentation-pymongo>=0.42b0",  # MongoDB instrumentation
    "opentelemetry-instrumentation-httpx>=0.42b0",  # HTTPX instrumentation
    
    # AI/ML integration
    "openai>=1.6.0",  # OpenAI API client
    "tiktoken>=0.5.2",  # OpenAI tokenizer
    
    # Development and testing utilities
    "structlog>=23.2.0",  # Structured logging
    "colorlog>=6.8.0",  # Colored logging
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.0",  # For testing HTTP clients
    "pytest-xdist>=3.5.0",  # Parallel test execution
    
    # Code quality
    "black>=23.12.0",  # Code formatting
    "isort>=5.13.0",  # Import sorting
    "flake8>=6.1.0",  # Linting
    "mypy>=1.8.0",  # Type checking
    "pre-commit>=3.6.0",  # Pre-commit hooks
    
    # Documentation
    "mkdocs>=1.5.0",  # Documentation generator
    "mkdocs-material>=9.5.0",  # Material theme
    "mkdocstrings[python]>=0.24.0",  # API documentation
]

docker = [
    "gunicorn>=21.2.0",  # WSGI server for production
]

all = [
    "microcks-python[dev,docker]"
]

[project.urls]
Homepage = "https://github.com/microcks/microcks-python"
Documentation = "https://microcks-python.readthedocs.io"
Repository = "https://github.com/microcks/microcks-python"
"Bug Tracker" = "https://github.com/microcks/microcks-python/issues"

[project.scripts]
microcks = "microcks.cli.main:app"
microcks-server = "microcks.main:main"

[tool.hatch.version]
path = "microcks/__init__.py"

[tool.hatch.build.targets.wheel]
packages = ["microcks"]

[tool.black]
line-length = 100
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "motor.*",
    "aiokafka.*",
    "aio_pika.*",
    "asyncio_mqtt.*",
    "nats.*",
    "grpcio.*",
    "opentelemetry.*",
    "prometheus_client.*"
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "asyncio: marks tests as async tests"
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["microcks"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
