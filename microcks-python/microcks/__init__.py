"""
Microcks Python - API Mocking and Testing Platform

A Python/FastAPI implementation of Microcks, providing comprehensive
API mocking and testing capabilities for REST, GraphQL, gRPC, AsyncAPI,
and other protocols.
"""

__version__ = "0.1.0"
__author__ = "Microcks Python Team"
__email__ = "<EMAIL>"
__license__ = "Apache-2.0"
__url__ = "https://github.com/microcks/microcks-python"

# Version info tuple for programmatic access
VERSION = tuple(map(int, __version__.split(".")))

# Package metadata
__all__ = [
    "__version__",
    "__author__",
    "__email__",
    "__license__",
    "__url__",
    "VERSION",
]
