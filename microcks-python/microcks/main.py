"""
Main application entry point for Microcks Python.

This module sets up the FastAPI application with all necessary middleware,
routers, and configuration.
"""

import logging
import sys
from contextlib import asynccontextmanager
from typing import AsyncGenerator

import uvicorn
from fastapi import <PERSON>AP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from prometheus_client import make_asgi_app

from microcks.core.config import get_settings
from microcks.core.database import close_database_connection, init_database_connection
from microcks.core.exceptions import MicrocksException
from microcks.core.logging import setup_logging
from microcks.api.routes import api_router
from microcks.api.mock_routes import mock_router


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager.
    
    Handles startup and shutdown events for the FastAPI application.
    """
    # Startup
    logging.info("Starting Microcks Python application...")
    
    # Initialize database connections
    await init_database_connection()
    
    logging.info("Microcks Python application started successfully")
    
    yield
    
    # Shutdown
    logging.info("Shutting down Microcks Python application...")
    
    # Close database connections
    await close_database_connection()
    
    logging.info("Microcks Python application shut down successfully")


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        FastAPI: Configured FastAPI application instance
    """
    settings = get_settings()
    
    # Setup logging
    setup_logging(
        level=settings.log_level,
        format_type=settings.log_format,
        log_file=settings.log_file
    )
    
    # Create FastAPI app
    app = FastAPI(
        title="Microcks Python API",
        description="API mocking and testing platform built with Python and FastAPI",
        version="0.1.0",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None,
        lifespan=lifespan,
    )
    
    # Add middleware
    setup_middleware(app, settings)
    
    # Add routers
    app.include_router(api_router, prefix="/api")
    app.include_router(mock_router)
    
    # Add exception handlers
    setup_exception_handlers(app)
    
    # Add metrics endpoint if enabled
    if settings.enable_metrics:
        metrics_app = make_asgi_app()
        app.mount(settings.metrics_path, metrics_app)
    
    return app


def setup_middleware(app: FastAPI, settings) -> None:
    """
    Setup middleware for the FastAPI application.
    
    Args:
        app: FastAPI application instance
        settings: Application settings
    """
    # Trusted host middleware (security)
    if not settings.debug:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["*"]  # Configure based on your needs
        )
    
    # CORS middleware
    if settings.enable_cors:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=settings.cors_allowed_origins,
            allow_credentials=settings.cors_allow_credentials,
            allow_methods=settings.cors_allowed_methods,
            allow_headers=settings.cors_allowed_headers,
        )
    
    # Gzip compression middleware
    app.add_middleware(GZipMiddleware, minimum_size=1000)


def setup_exception_handlers(app: FastAPI) -> None:
    """
    Setup global exception handlers.
    
    Args:
        app: FastAPI application instance
    """
    @app.exception_handler(MicrocksException)
    async def microcks_exception_handler(request: Request, exc: MicrocksException):
        """Handle Microcks-specific exceptions."""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": exc.error_code,
                "message": exc.message,
                "details": exc.details
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle general exceptions."""
        logging.exception("Unhandled exception occurred")
        return JSONResponse(
            status_code=500,
            content={
                "error": "INTERNAL_SERVER_ERROR",
                "message": "An internal server error occurred"
            }
        )


# Create the app instance
app = create_app()


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "version": "0.1.0",
        "service": "microcks-python"
    }


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Welcome to Microcks Python",
        "version": "0.1.0",
        "docs": "/docs",
        "health": "/health"
    }


def main() -> None:
    """
    Main entry point for running the application.
    
    This function is used when running the application directly
    or via the CLI command.
    """
    settings = get_settings()
    
    # Configure uvicorn logging
    log_config = uvicorn.config.LOGGING_CONFIG
    log_config["formatters"]["default"]["fmt"] = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    log_config["formatters"]["access"]["fmt"] = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Run the application
    uvicorn.run(
        "microcks.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload and settings.debug,
        log_level=settings.log_level.lower(),
        log_config=log_config,
        access_log=True,
    )


if __name__ == "__main__":
    main()
