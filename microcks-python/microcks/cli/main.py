"""
Main CLI application for Microcks Python.

This module provides command-line interface functionality for managing
Microcks services, tests, and other operations.
"""

import typer
from rich.console import Console
from rich.table import Table
from typing import Optional

from microcks import __version__

app = typer.Typer(
    name="microcks",
    help="Microcks Python - API Mocking and Testing Platform",
    add_completion=False,
)
console = Console()


@app.command()
def version():
    """Show version information."""
    console.print(f"Microcks Python v{__version__}")


@app.command()
def server(
    host: str = typer.Option("0.0.0.0", "--host", "-h", help="Server host"),
    port: int = typer.Option(8080, "--port", "-p", help="Server port"),
    reload: bool = typer.Option(False, "--reload", "-r", help="Enable auto-reload"),
    debug: bool = typer.Option(False, "--debug", "-d", help="Enable debug mode"),
):
    """Start the Microcks server."""
    import uvicorn
    from microcks.main import app as fastapi_app
    
    console.print(f"Starting Microcks Python server on {host}:{port}")
    
    uvicorn.run(
        "microcks.main:app",
        host=host,
        port=port,
        reload=reload,
        log_level="debug" if debug else "info",
    )


@app.command()
def import_spec(
    file_path: str = typer.Argument(..., help="Path to the specification file"),
    spec_type: Optional[str] = typer.Option(None, "--type", "-t", help="Specification type"),
    main_artifact: bool = typer.Option(True, "--main", help="Mark as main artifact"),
):
    """Import an API specification file."""
    console.print(f"Importing specification: {file_path}")
    console.print("[yellow]Import functionality not implemented yet[/yellow]")


@app.command()
def test(
    service: str = typer.Argument(..., help="Service name"),
    version: str = typer.Argument(..., help="Service version"),
    endpoint: str = typer.Option(..., "--endpoint", "-e", help="Test endpoint URL"),
    runner: str = typer.Option("OPEN_API_SCHEMA", "--runner", "-r", help="Test runner type"),
    timeout: Optional[int] = typer.Option(None, "--timeout", help="Test timeout in seconds"),
):
    """Run tests against a service."""
    console.print(f"Testing service: {service}:{version}")
    console.print(f"Endpoint: {endpoint}")
    console.print(f"Runner: {runner}")
    console.print("[yellow]Test functionality not implemented yet[/yellow]")


@app.command()
def list_services():
    """List all services."""
    console.print("Listing services...")
    
    # Create a sample table
    table = Table(title="Services")
    table.add_column("Name", style="cyan")
    table.add_column("Version", style="magenta")
    table.add_column("Type", style="green")
    table.add_column("Operations", justify="right", style="yellow")
    
    # TODO: Replace with actual service data
    table.add_row("Sample API", "1.0.0", "REST", "5")
    
    console.print(table)
    console.print("[yellow]Service listing not fully implemented yet[/yellow]")


@app.command()
def health():
    """Check application health."""
    console.print("Checking application health...")
    console.print("[yellow]Health check not implemented yet[/yellow]")


if __name__ == "__main__":
    app()
