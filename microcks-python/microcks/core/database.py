"""
Database connection and configuration for Microcks Python.

This module handles MongoDB and Redis connections using async drivers
with proper connection pooling and error handling.
"""

import asyncio
from typing import Optional

import redis.asyncio as redis
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

from microcks.core.config import get_settings
from microcks.core.exceptions import DatabaseError
from microcks.core.logging import get_logger

logger = get_logger(__name__)

# Global database connections
_mongodb_client: Optional[AsyncIOMotorClient] = None
_mongodb_database: Optional[AsyncIOMotorDatabase] = None
_redis_client: Optional[redis.Redis] = None


async def init_database_connection() -> None:
    """
    Initialize database connections.
    
    Sets up MongoDB and Redis connections with proper configuration
    and connection pooling.
    
    Raises:
        DatabaseError: If database connection fails
    """
    global _mongodb_client, _mongodb_database, _redis_client
    
    settings = get_settings()
    
    try:
        # Initialize MongoDB connection
        logger.info("Initializing MongoDB connection", url=settings.mongodb_url)
        
        _mongodb_client = AsyncIOMotorClient(
            settings.mongodb_url,
            minPoolSize=settings.mongodb_min_pool_size,
            maxPoolSize=settings.mongodb_max_pool_size,
            serverSelectionTimeoutMS=5000,  # 5 seconds
            connectTimeoutMS=10000,  # 10 seconds
            socketTimeoutMS=30000,  # 30 seconds
        )
        
        # Test MongoDB connection
        await _mongodb_client.admin.command('ping')
        _mongodb_database = _mongodb_client[settings.mongodb_database]
        
        logger.info(
            "MongoDB connection established successfully",
            database=settings.mongodb_database
        )
        
        # Initialize indexes
        await _create_mongodb_indexes()
        
    except (ConnectionFailure, ServerSelectionTimeoutError) as e:
        logger.error("Failed to connect to MongoDB", error=str(e))
        raise DatabaseError(
            message="Failed to connect to MongoDB",
            operation="connection",
            details={"error": str(e)}
        )
    
    # Initialize Redis connection (optional)
    if settings.redis_url:
        try:
            logger.info("Initializing Redis connection", url=settings.redis_url)
            
            _redis_client = redis.from_url(
                settings.redis_url,
                db=settings.redis_db,
                password=settings.redis_password,
                ssl=settings.redis_ssl,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=30,
                retry_on_timeout=True,
                health_check_interval=30,
            )
            
            # Test Redis connection
            await _redis_client.ping()
            
            logger.info("Redis connection established successfully")
            
        except Exception as e:
            logger.warning(
                "Failed to connect to Redis, continuing without cache",
                error=str(e)
            )
            _redis_client = None


async def close_database_connection() -> None:
    """
    Close database connections.
    
    Properly closes MongoDB and Redis connections to prevent
    connection leaks.
    """
    global _mongodb_client, _mongodb_database, _redis_client
    
    # Close MongoDB connection
    if _mongodb_client:
        logger.info("Closing MongoDB connection")
        _mongodb_client.close()
        _mongodb_client = None
        _mongodb_database = None
    
    # Close Redis connection
    if _redis_client:
        logger.info("Closing Redis connection")
        await _redis_client.close()
        _redis_client = None


def get_mongodb_database() -> AsyncIOMotorDatabase:
    """
    Get MongoDB database instance.
    
    Returns:
        AsyncIOMotorDatabase: MongoDB database instance
        
    Raises:
        DatabaseError: If database is not initialized
    """
    if _mongodb_database is None:
        raise DatabaseError(
            message="MongoDB database not initialized",
            operation="get_database"
        )
    return _mongodb_database


def get_redis_client() -> Optional[redis.Redis]:
    """
    Get Redis client instance.
    
    Returns:
        Optional[Redis]: Redis client instance or None if not available
    """
    return _redis_client


async def _create_mongodb_indexes() -> None:
    """
    Create MongoDB indexes for optimal performance.
    
    This function creates all necessary indexes for the collections
    used by Microcks Python.
    """
    if not _mongodb_database:
        return
    
    logger.info("Creating MongoDB indexes")
    
    try:
        # Services collection indexes
        services_collection = _mongodb_database.services
        await services_collection.create_index([("name", 1), ("version", 1)], unique=True)
        await services_collection.create_index([("type", 1)])
        await services_collection.create_index([("metadata.labels", 1)])
        await services_collection.create_index([("created_at", -1)])
        await services_collection.create_index([("updated_at", -1)])
        
        # Operations collection indexes
        operations_collection = _mongodb_database.operations
        await operations_collection.create_index([("service_id", 1)])
        await operations_collection.create_index([("name", 1)])
        await operations_collection.create_index([("method", 1)])
        
        # Requests collection indexes
        requests_collection = _mongodb_database.requests
        await requests_collection.create_index([("operation_id", 1)])
        await requests_collection.create_index([("service_id", 1)])
        await requests_collection.create_index([("created_at", -1)])
        
        # Responses collection indexes
        responses_collection = _mongodb_database.responses
        await responses_collection.create_index([("operation_id", 1)])
        await responses_collection.create_index([("service_id", 1)])
        await responses_collection.create_index([("dispatch_criteria", 1)])
        await responses_collection.create_index([("created_at", -1)])
        
        # Test results collection indexes
        test_results_collection = _mongodb_database.test_results
        await test_results_collection.create_index([("service_id", 1)])
        await test_results_collection.create_index([("test_number", -1)])
        await test_results_collection.create_index([("test_date", -1)])
        await test_results_collection.create_index([("success", 1)])
        
        # Import jobs collection indexes
        import_jobs_collection = _mongodb_database.import_jobs
        await import_jobs_collection.create_index([("name", 1)], unique=True)
        await import_jobs_collection.create_index([("active", 1)])
        await import_jobs_collection.create_index([("created_date", -1)])
        await import_jobs_collection.create_index([("last_import_date", -1)])
        
        # Resources collection indexes
        resources_collection = _mongodb_database.resources
        await resources_collection.create_index([("service_id", 1)])
        await resources_collection.create_index([("type", 1)])
        await resources_collection.create_index([("name", 1)])
        
        # Generic resources collection indexes (for dynamic mocking)
        generic_resources_collection = _mongodb_database.generic_resources
        await generic_resources_collection.create_index([("service_id", 1)])
        await generic_resources_collection.create_index([("created_at", -1)])
        
        # Daily statistics collection indexes
        daily_statistics_collection = _mongodb_database.daily_statistics
        await daily_statistics_collection.create_index([("day", 1)])
        await daily_statistics_collection.create_index([("service_name", 1), ("service_version", 1)])
        await daily_statistics_collection.create_index([("day", 1), ("service_name", 1), ("service_version", 1)])
        
        # Users collection indexes (for authentication)
        users_collection = _mongodb_database.users
        await users_collection.create_index([("username", 1)], unique=True)
        await users_collection.create_index([("email", 1)], unique=True)
        await users_collection.create_index([("created_at", -1)])
        
        # Secrets collection indexes
        secrets_collection = _mongodb_database.secrets
        await secrets_collection.create_index([("name", 1)], unique=True)
        await secrets_collection.create_index([("created_at", -1)])
        
        logger.info("MongoDB indexes created successfully")
        
    except Exception as e:
        logger.error("Failed to create MongoDB indexes", error=str(e))
        # Don't raise exception here as indexes are not critical for startup


async def health_check() -> dict:
    """
    Perform database health check.
    
    Returns:
        dict: Health check results for all database connections
    """
    health_status = {
        "mongodb": {"status": "unknown", "details": {}},
        "redis": {"status": "unknown", "details": {}}
    }
    
    # Check MongoDB
    try:
        if _mongodb_client and _mongodb_database:
            await _mongodb_client.admin.command('ping')
            server_info = await _mongodb_client.server_info()
            health_status["mongodb"] = {
                "status": "healthy",
                "details": {
                    "version": server_info.get("version"),
                    "database": _mongodb_database.name
                }
            }
        else:
            health_status["mongodb"] = {
                "status": "disconnected",
                "details": {"message": "MongoDB client not initialized"}
            }
    except Exception as e:
        health_status["mongodb"] = {
            "status": "unhealthy",
            "details": {"error": str(e)}
        }
    
    # Check Redis
    try:
        if _redis_client:
            await _redis_client.ping()
            info = await _redis_client.info()
            health_status["redis"] = {
                "status": "healthy",
                "details": {
                    "version": info.get("redis_version"),
                    "connected_clients": info.get("connected_clients")
                }
            }
        else:
            health_status["redis"] = {
                "status": "not_configured",
                "details": {"message": "Redis not configured"}
            }
    except Exception as e:
        health_status["redis"] = {
            "status": "unhealthy",
            "details": {"error": str(e)}
        }
    
    return health_status
