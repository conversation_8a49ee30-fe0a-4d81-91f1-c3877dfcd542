"""
Configuration management for Microcks Python.

This module handles all application configuration using Pydantic settings
with support for environment variables and configuration files.
"""

import logging
from functools import lru_cache
from typing import List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """
    Application settings with environment variable support.
    
    All settings can be overridden using environment variables with
    the same name (case-insensitive).
    """
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    # Server Configuration
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8080, description="Server port")
    debug: bool = Field(default=False, description="Debug mode")
    reload: bool = Field(default=False, description="Auto-reload on code changes")
    
    # Database Configuration
    mongodb_url: str = Field(
        default="mongodb://localhost:27017",
        description="MongoDB connection URL"
    )
    mongodb_database: str = Field(
        default="microcks",
        description="MongoDB database name"
    )
    mongodb_min_pool_size: int = Field(
        default=10,
        description="MongoDB minimum connection pool size"
    )
    mongodb_max_pool_size: int = Field(
        default=100,
        description="MongoDB maximum connection pool size"
    )
    
    # Redis Configuration (optional)
    redis_url: Optional[str] = Field(
        default=None,
        description="Redis connection URL"
    )
    redis_db: int = Field(default=0, description="Redis database number")
    redis_password: Optional[str] = Field(default=None, description="Redis password")
    redis_ssl: bool = Field(default=False, description="Use SSL for Redis connection")
    
    # Authentication & Security
    jwt_secret_key: str = Field(
        default="your-secret-key-change-this-in-production",
        description="JWT secret key"
    )
    jwt_algorithm: str = Field(default="HS256", description="JWT algorithm")
    jwt_access_token_expire_minutes: int = Field(
        default=30,
        description="JWT access token expiration in minutes"
    )
    jwt_refresh_token_expire_days: int = Field(
        default=7,
        description="JWT refresh token expiration in days"
    )
    
    # Password hashing
    password_hash_algorithm: str = Field(
        default="bcrypt",
        description="Password hashing algorithm"
    )
    password_hash_rounds: int = Field(
        default=12,
        description="Password hashing rounds"
    )
    
    # CORS Configuration
    enable_cors: bool = Field(default=True, description="Enable CORS")
    cors_allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="CORS allowed origins"
    )
    cors_allowed_methods: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
        description="CORS allowed methods"
    )
    cors_allowed_headers: List[str] = Field(
        default=["*"],
        description="CORS allowed headers"
    )
    cors_allow_credentials: bool = Field(
        default=True,
        description="CORS allow credentials"
    )
    
    # Features Configuration
    enable_async_api: bool = Field(default=True, description="Enable AsyncAPI support")
    default_async_binding: str = Field(
        default="KAFKA",
        description="Default async binding"
    )
    default_async_frequency: int = Field(
        default=3,
        description="Default async frequency"
    )
    
    # AI Copilot Configuration
    enable_ai_copilot: bool = Field(default=False, description="Enable AI Copilot")
    ai_copilot_provider: str = Field(default="openai", description="AI provider")
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API key")
    openai_model: str = Field(default="gpt-3.5-turbo", description="OpenAI model")
    openai_max_tokens: int = Field(default=3000, description="OpenAI max tokens")
    openai_timeout: int = Field(default=30, description="OpenAI timeout")
    
    # Dynamic Mocking
    enable_dynamic_mocking: bool = Field(
        default=True,
        description="Enable dynamic mocking"
    )
    dynamic_mock_ttl: int = Field(
        default=3600,
        description="Dynamic mock TTL in seconds"
    )
    
    # Import/Export
    enable_import_export: bool = Field(
        default=True,
        description="Enable import/export features"
    )
    max_upload_size: int = Field(
        default=10485760,  # 10MB
        description="Maximum upload size in bytes"
    )
    allowed_upload_extensions: List[str] = Field(
        default=[".json", ".yaml", ".yml", ".xml", ".proto", ".graphql", ".har"],
        description="Allowed upload file extensions"
    )
    
    # Protocol Support
    enable_rest_mocking: bool = Field(default=True, description="Enable REST mocking")
    rest_mock_enable_validation: bool = Field(
        default=True,
        description="Enable REST mock validation"
    )
    rest_mock_enable_cors: bool = Field(
        default=True,
        description="Enable CORS for REST mocks"
    )
    
    enable_graphql: bool = Field(default=True, description="Enable GraphQL support")
    graphql_introspection: bool = Field(
        default=True,
        description="Enable GraphQL introspection"
    )
    graphql_playground: bool = Field(
        default=True,
        description="Enable GraphQL playground"
    )
    
    enable_grpc: bool = Field(default=True, description="Enable gRPC support")
    grpc_port: int = Field(default=9090, description="gRPC server port")
    grpc_reflection: bool = Field(default=True, description="Enable gRPC reflection")
    
    enable_websocket: bool = Field(default=True, description="Enable WebSocket support")
    websocket_ping_interval: int = Field(
        default=20,
        description="WebSocket ping interval"
    )
    websocket_ping_timeout: int = Field(
        default=10,
        description="WebSocket ping timeout"
    )
    
    # Monitoring & Observability
    enable_metrics: bool = Field(default=True, description="Enable metrics")
    metrics_path: str = Field(default="/metrics", description="Metrics endpoint path")
    
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Log format (json or text)")
    log_file: Optional[str] = Field(default=None, description="Log file path")
    log_rotation: bool = Field(default=True, description="Enable log rotation")
    log_max_size: str = Field(default="100MB", description="Maximum log file size")
    log_backup_count: int = Field(default=5, description="Number of log backups")
    
    # OpenTelemetry
    enable_tracing: bool = Field(default=False, description="Enable tracing")
    otel_service_name: str = Field(
        default="microcks-python",
        description="OpenTelemetry service name"
    )
    otel_exporter_otlp_endpoint: Optional[str] = Field(
        default=None,
        description="OTLP exporter endpoint"
    )
    
    # Performance & Scaling
    max_request_size: int = Field(
        default=10485760,  # 10MB
        description="Maximum request size in bytes"
    )
    request_timeout: int = Field(default=30, description="Request timeout in seconds")
    response_timeout: int = Field(default=30, description="Response timeout in seconds")
    
    http_pool_connections: int = Field(
        default=100,
        description="HTTP connection pool size"
    )
    http_pool_maxsize: int = Field(
        default=100,
        description="HTTP connection pool max size"
    )
    http_max_retries: int = Field(default=3, description="HTTP max retries")
    
    # Caching
    enable_response_caching: bool = Field(
        default=True,
        description="Enable response caching"
    )
    cache_ttl: int = Field(default=300, description="Cache TTL in seconds")
    cache_max_size: int = Field(default=1000, description="Cache max size")
    
    # Rate Limiting
    enable_rate_limiting: bool = Field(default=False, description="Enable rate limiting")
    rate_limit_requests: int = Field(
        default=1000,
        description="Rate limit requests per window"
    )
    rate_limit_window: int = Field(
        default=3600,
        description="Rate limit window in seconds"
    )
    
    @validator("log_level")
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level. Must be one of: {valid_levels}")
        return v.upper()
    
    @validator("log_format")
    def validate_log_format(cls, v):
        """Validate log format."""
        valid_formats = ["json", "text"]
        if v.lower() not in valid_formats:
            raise ValueError(f"Invalid log format. Must be one of: {valid_formats}")
        return v.lower()
    
    @validator("cors_allowed_origins", pre=True)
    def parse_cors_origins(cls, v):
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("cors_allowed_methods", pre=True)
    def parse_cors_methods(cls, v):
        """Parse CORS methods from string or list."""
        if isinstance(v, str):
            return [method.strip() for method in v.split(",")]
        return v
    
    @validator("cors_allowed_headers", pre=True)
    def parse_cors_headers(cls, v):
        """Parse CORS headers from string or list."""
        if isinstance(v, str):
            return [header.strip() for header in v.split(",")]
        return v
    
    @validator("allowed_upload_extensions", pre=True)
    def parse_upload_extensions(cls, v):
        """Parse upload extensions from string or list."""
        if isinstance(v, str):
            return [ext.strip() for ext in v.split(",")]
        return v


@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings with caching.
    
    Returns:
        Settings: Application settings instance
    """
    return Settings()
