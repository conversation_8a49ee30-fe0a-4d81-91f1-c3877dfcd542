"""
Custom exceptions for Microcks Python.

This module defines all custom exceptions used throughout the application
with proper error codes and HTTP status codes.
"""

from typing import Any, Dict, Optional


class MicrocksException(Exception):
    """
    Base exception class for all Microcks-specific exceptions.
    
    Attributes:
        message: Human-readable error message
        error_code: Machine-readable error code
        status_code: HTTP status code
        details: Additional error details
    """
    
    def __init__(
        self,
        message: str,
        error_code: str = "MICROCKS_ERROR",
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(MicrocksException):
    """Exception raised for validation errors."""
    
    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            status_code=400,
            details=details
        )


class NotFoundError(MicrocksException):
    """Exception raised when a resource is not found."""
    
    def __init__(
        self,
        message: str,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None
    ):
        details = {}
        if resource_type:
            details["resource_type"] = resource_type
        if resource_id:
            details["resource_id"] = resource_id
            
        super().__init__(
            message=message,
            error_code="NOT_FOUND",
            status_code=404,
            details=details
        )


class ConflictError(MicrocksException):
    """Exception raised when a resource conflict occurs."""
    
    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="CONFLICT",
            status_code=409,
            details=details
        )


class AuthenticationError(MicrocksException):
    """Exception raised for authentication failures."""
    
    def __init__(
        self,
        message: str = "Authentication failed",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            status_code=401,
            details=details
        )


class AuthorizationError(MicrocksException):
    """Exception raised for authorization failures."""
    
    def __init__(
        self,
        message: str = "Access denied",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            status_code=403,
            details=details
        )


class ServiceError(MicrocksException):
    """Exception raised for service-related errors."""
    
    def __init__(
        self,
        message: str,
        service_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        if details is None:
            details = {}
        if service_id:
            details["service_id"] = service_id
            
        super().__init__(
            message=message,
            error_code="SERVICE_ERROR",
            status_code=400,
            details=details
        )


class MockingError(MicrocksException):
    """Exception raised for mocking-related errors."""
    
    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        if details is None:
            details = {}
        if operation:
            details["operation"] = operation
            
        super().__init__(
            message=message,
            error_code="MOCKING_ERROR",
            status_code=400,
            details=details
        )


class TestingError(MicrocksException):
    """Exception raised for testing-related errors."""
    
    def __init__(
        self,
        message: str,
        test_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        if details is None:
            details = {}
        if test_id:
            details["test_id"] = test_id
            
        super().__init__(
            message=message,
            error_code="TESTING_ERROR",
            status_code=400,
            details=details
        )


class ImportError(MicrocksException):
    """Exception raised for import-related errors."""
    
    def __init__(
        self,
        message: str,
        file_type: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        if details is None:
            details = {}
        if file_type:
            details["file_type"] = file_type
            
        super().__init__(
            message=message,
            error_code="IMPORT_ERROR",
            status_code=400,
            details=details
        )


class ExportError(MicrocksException):
    """Exception raised for export-related errors."""
    
    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="EXPORT_ERROR",
            status_code=400,
            details=details
        )


class DatabaseError(MicrocksException):
    """Exception raised for database-related errors."""
    
    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        if details is None:
            details = {}
        if operation:
            details["operation"] = operation
            
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            status_code=500,
            details=details
        )


class ConfigurationError(MicrocksException):
    """Exception raised for configuration-related errors."""
    
    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        if details is None:
            details = {}
        if config_key:
            details["config_key"] = config_key
            
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            status_code=500,
            details=details
        )


class ExternalServiceError(MicrocksException):
    """Exception raised for external service errors."""
    
    def __init__(
        self,
        message: str,
        service_name: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        if details is None:
            details = {}
        if service_name:
            details["service_name"] = service_name
            
        super().__init__(
            message=message,
            error_code="EXTERNAL_SERVICE_ERROR",
            status_code=502,
            details=details
        )


class RateLimitError(MicrocksException):
    """Exception raised when rate limit is exceeded."""
    
    def __init__(
        self,
        message: str = "Rate limit exceeded",
        retry_after: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        if details is None:
            details = {}
        if retry_after:
            details["retry_after"] = retry_after
            
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_EXCEEDED",
            status_code=429,
            details=details
        )


class TimeoutError(MicrocksException):
    """Exception raised when an operation times out."""
    
    def __init__(
        self,
        message: str,
        timeout_seconds: Optional[float] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        if details is None:
            details = {}
        if timeout_seconds:
            details["timeout_seconds"] = timeout_seconds
            
        super().__init__(
            message=message,
            error_code="TIMEOUT_ERROR",
            status_code=408,
            details=details
        )
