"""
Logging configuration for Microcks Python.

This module provides structured logging setup with support for both
JSON and text formats, file rotation, and different log levels.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

import structlog
from structlog.stdlib import LoggerFactory


def setup_logging(
    level: str = "INFO",
    format_type: str = "json",
    log_file: Optional[str] = None,
    rotation: bool = True,
    max_size: str = "100MB",
    backup_count: int = 5
) -> None:
    """
    Setup application logging configuration.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        format_type: Log format type ("json" or "text")
        log_file: Optional log file path
        rotation: Enable log file rotation
        max_size: Maximum log file size before rotation
        backup_count: Number of backup files to keep
    """
    # Convert string level to logging constant
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if format_type == "json" 
            else structlog.dev.ConsoleRenderer(colors=True),
        ],
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    logging.basicConfig(
        level=numeric_level,
        format="%(message)s",
        handlers=[]
    )
    
    # Setup handlers
    handlers = []
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(numeric_level)
    handlers.append(console_handler)
    
    # File handler (if specified)
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        if rotation:
            # Parse max_size (e.g., "100MB" -> 100 * 1024 * 1024)
            size_multipliers = {
                'B': 1,
                'KB': 1024,
                'MB': 1024 * 1024,
                'GB': 1024 * 1024 * 1024
            }
            
            size_str = max_size.upper()
            for suffix, multiplier in size_multipliers.items():
                if size_str.endswith(suffix):
                    max_bytes = int(size_str[:-len(suffix)]) * multiplier
                    break
            else:
                max_bytes = 100 * 1024 * 1024  # Default to 100MB
            
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=max_bytes,
                backupCount=backup_count,
                encoding='utf-8'
            )
        else:
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
        
        file_handler.setLevel(numeric_level)
        handlers.append(file_handler)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    for handler in handlers:
        root_logger.addHandler(handler)
    root_logger.setLevel(numeric_level)
    
    # Configure specific loggers
    configure_third_party_loggers(numeric_level)


def configure_third_party_loggers(level: int) -> None:
    """
    Configure logging levels for third-party libraries.
    
    Args:
        level: Base logging level
    """
    # Reduce noise from third-party libraries
    third_party_loggers = {
        'uvicorn': level,
        'uvicorn.access': logging.WARNING,
        'uvicorn.error': level,
        'fastapi': level,
        'motor': logging.WARNING,
        'pymongo': logging.WARNING,
        'httpx': logging.WARNING,
        'asyncio': logging.WARNING,
        'websockets': logging.WARNING,
        'aiokafka': logging.WARNING,
        'openai': logging.WARNING,
    }
    
    for logger_name, logger_level in third_party_loggers.items():
        logging.getLogger(logger_name).setLevel(logger_level)


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        BoundLogger: Structured logger instance
    """
    return structlog.get_logger(name)


class LoggerMixin:
    """
    Mixin class to add logging capabilities to any class.
    
    Provides a `logger` property that returns a structured logger
    with the class name as the logger name.
    """
    
    @property
    def logger(self) -> structlog.stdlib.BoundLogger:
        """Get logger for this class."""
        return get_logger(self.__class__.__name__)


# Convenience function for getting module-level loggers
def get_module_logger() -> structlog.stdlib.BoundLogger:
    """
    Get a logger for the calling module.
    
    Returns:
        BoundLogger: Structured logger instance
    """
    import inspect
    frame = inspect.currentframe()
    if frame and frame.f_back:
        module_name = frame.f_back.f_globals.get('__name__', 'unknown')
        return get_logger(module_name)
    return get_logger('unknown')
