"""WebSocket mock endpoints - placeholder implementation."""

from fastapi import APIRouter, WebSocket
from microcks.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()

@router.websocket("/ws/{service}/{version}")
async def websocket_mock(websocket: WebSocket, service: str, version: str):
    """Handle WebSocket mock connections."""
    await websocket.accept()
    await websocket.send_text(f"WebSocket mock for {service}:{version} not implemented yet")
    await websocket.close()
