"""REST mock endpoints - placeholder implementation."""

from fastapi import APIRouter, Request
from microcks.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()

@router.api_route("/rest/{service}/{version}/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"])
async def rest_mock(service: str, version: str, path: str, request: Request):
    """Handle REST mock requests."""
    return {"message": "REST mock not implemented yet", "service": service, "version": version, "path": path}

@router.api_route("/rest-valid/{service}/{version}/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"])
async def rest_mock_with_validation(service: str, version: str, path: str, request: Request):
    """Handle REST mock requests with validation."""
    return {"message": "REST mock with validation not implemented yet", "service": service, "version": version, "path": path}
