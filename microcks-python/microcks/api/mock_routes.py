"""
Mock API router for Microcks Python.

This module defines the mock endpoints that serve actual mock responses
for REST, GraphQL, gRPC, and other protocols.
"""

from fastapi import APIRouter

from microcks.api.mock_endpoints import (
    rest_mock,
    graphql_mock,
    grpc_mock,
    websocket_mock,
    dynamic_mock
)

# Create mock router
mock_router = APIRouter()

# Include all mock endpoint routers
mock_router.include_router(
    rest_mock.router,
    tags=["rest-mocks"]
)

mock_router.include_router(
    graphql_mock.router,
    tags=["graphql-mocks"]
)

mock_router.include_router(
    grpc_mock.router,
    tags=["grpc-mocks"]
)

mock_router.include_router(
    websocket_mock.router,
    tags=["websocket-mocks"]
)

mock_router.include_router(
    dynamic_mock.router,
    prefix="/dynarest",
    tags=["dynamic-mocks"]
)
