"""
Main API router for Microcks Python.

This module defines the main API router that includes all sub-routers
for different API endpoints.
"""

from fastapi import APIRouter

from microcks.api.endpoints import (
    services,
    tests,
    jobs,
    metrics,
    config,
    import_export,
    health
)

# Create main API router
api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(
    services.router,
    prefix="/services",
    tags=["services"]
)

api_router.include_router(
    tests.router,
    prefix="/tests",
    tags=["tests"]
)

api_router.include_router(
    jobs.router,
    prefix="/jobs",
    tags=["jobs"]
)

api_router.include_router(
    metrics.router,
    prefix="/metrics",
    tags=["metrics"]
)

api_router.include_router(
    config.router,
    prefix="/config",
    tags=["config"]
)

api_router.include_router(
    import_export.router,
    tags=["import-export"]
)

api_router.include_router(
    health.router,
    tags=["health"]
)
