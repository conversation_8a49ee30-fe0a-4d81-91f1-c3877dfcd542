"""Metrics API endpoints - placeholder implementation."""

from fastapi import APIRouter
from microcks.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()

@router.get("/invocations/global")
async def get_global_invocation_stats():
    """Get global invocation statistics."""
    return {"message": "Not implemented yet"}

@router.get("/invocations/{service}/{version}")
async def get_service_invocation_stats(service: str, version: str):
    """Get service-specific invocation statistics."""
    return {"message": "Not implemented yet"}

@router.get("/conformance/aggregate")
async def get_conformance_metrics():
    """Get aggregated conformance metrics."""
    return {"message": "Not implemented yet"}
