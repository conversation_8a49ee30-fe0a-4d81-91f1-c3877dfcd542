"""
Health check endpoints for Microcks Python.

This module provides health check endpoints for monitoring the
application status and database connections.
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any

from microcks.core.database import health_check as db_health_check
from microcks.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str
    version: str
    service: str
    checks: Dict[str, Any]


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """
    Comprehensive health check endpoint.
    
    Returns the overall health status of the application including
    database connections and other critical components.
    """
    try:
        # Perform database health checks
        db_health = await db_health_check()
        
        # Determine overall status
        overall_status = "healthy"
        for service_name, service_health in db_health.items():
            if service_health["status"] in ["unhealthy", "disconnected"]:
                overall_status = "unhealthy"
                break
            elif service_health["status"] == "unknown":
                overall_status = "degraded"
        
        return HealthResponse(
            status=overall_status,
            version="0.1.0",
            service="microcks-python",
            checks=db_health
        )
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(
            status_code=503,
            detail={
                "status": "unhealthy",
                "error": "Health check failed",
                "message": str(e)
            }
        )


@router.get("/health/ready")
async def readiness_check():
    """
    Kubernetes readiness probe endpoint.
    
    Returns 200 if the application is ready to serve requests,
    503 otherwise.
    """
    try:
        db_health = await db_health_check()
        
        # Check if MongoDB is healthy (required)
        mongodb_status = db_health.get("mongodb", {}).get("status")
        if mongodb_status not in ["healthy"]:
            raise HTTPException(
                status_code=503,
                detail={"status": "not_ready", "reason": "Database not available"}
            )
        
        return {"status": "ready"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        raise HTTPException(
            status_code=503,
            detail={"status": "not_ready", "reason": str(e)}
        )


@router.get("/health/live")
async def liveness_check():
    """
    Kubernetes liveness probe endpoint.
    
    Returns 200 if the application is alive and running,
    503 if it should be restarted.
    """
    # Simple liveness check - if we can respond, we're alive
    return {"status": "alive"}
