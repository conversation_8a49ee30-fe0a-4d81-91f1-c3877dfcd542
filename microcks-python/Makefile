# Microcks Python Makefile

.PHONY: help install install-dev test test-cov lint format type-check clean build run docker-build docker-run docs

# Default target
help:
	@echo "Available commands:"
	@echo "  install      Install production dependencies"
	@echo "  install-dev  Install development dependencies"
	@echo "  test         Run tests"
	@echo "  test-cov     Run tests with coverage"
	@echo "  lint         Run linting"
	@echo "  format       Format code"
	@echo "  type-check   Run type checking"
	@echo "  clean        Clean build artifacts"
	@echo "  build        Build package"
	@echo "  run          Run development server"
	@echo "  docker-build Build Docker image"
	@echo "  docker-run   Run with Docker Compose"
	@echo "  docs         Build documentation"

# Installation
install:
	pip install -e .

install-dev:
	pip install -e ".[dev]"

# Testing
test:
	pytest

test-cov:
	pytest --cov=microcks --cov-report=html --cov-report=term

# Code quality
lint:
	flake8 microcks tests
	isort --check-only microcks tests
	black --check microcks tests

format:
	isort microcks tests
	black microcks tests

type-check:
	mypy microcks

# Development
run:
	python -m microcks.main

run-cli:
	microcks server --reload --debug

# Build and packaging
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete

build: clean
	python -m build

# Docker
docker-build:
	docker build -t microcks-python .

docker-run:
	docker-compose up -d

docker-stop:
	docker-compose down

docker-logs:
	docker-compose logs -f microcks-app

# Documentation
docs:
	mkdocs build

docs-serve:
	mkdocs serve

# Database
db-init:
	python -c "from microcks.core.database import init_database_connection; import asyncio; asyncio.run(init_database_connection())"

# All quality checks
check: lint type-check test

# CI/CD pipeline
ci: install-dev check test-cov

# Development setup
setup: install-dev
	pre-commit install
	@echo "Development environment setup complete!"
	@echo "Run 'make run' to start the development server"
