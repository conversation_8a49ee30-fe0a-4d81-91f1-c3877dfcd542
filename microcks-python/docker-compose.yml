version: '3.8'

services:
  # MongoDB database
  mongodb:
    image: mongo:7.0
    container_name: microcks-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: microcks
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongodb/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - microcks-network

  # Redis cache (optional)
  redis:
    image: redis:7.2-alpine
    container_name: microcks-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - microcks-network

  # Microcks Python application
  microcks-app:
    build:
      context: .
      dockerfile: docker/Dockerfile
      target: development
    container_name: microcks-python-app
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      # Database Configuration
      MONGODB_URL: ****************************************************************
      REDIS_URL: redis://redis:6379
      
      # Server Configuration
      HOST: 0.0.0.0
      PORT: 8080
      DEBUG: true
      
      # Authentication
      JWT_SECRET_KEY: dev-secret-key-change-in-production
      JWT_ALGORITHM: HS256
      JWT_ACCESS_TOKEN_EXPIRE_MINUTES: 30
      
      # Features
      ENABLE_ASYNC_API: true
      ENABLE_AI_COPILOT: false
      ENABLE_CORS: true
      CORS_ALLOWED_ORIGINS: "http://localhost:3000,http://localhost:8080"
      
      # Logging
      LOG_LEVEL: INFO
      LOG_FORMAT: json
      
      # Monitoring
      ENABLE_METRICS: true
      METRICS_PATH: /metrics
    volumes:
      - ./microcks:/app/microcks:ro
      - ./tests:/app/tests:ro
      - ./examples:/app/examples:ro
    depends_on:
      - mongodb
      - redis
    networks:
      - microcks-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Kafka for async API testing (optional)
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: microcks-zookeeper
    restart: unless-stopped
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - microcks-network
    profiles:
      - async

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: microcks-kafka
    restart: unless-stopped
    ports:
      - "9092:9092"
      - "9094:9094"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    volumes:
      - kafka_data:/var/lib/kafka/data
    depends_on:
      - zookeeper
    networks:
      - microcks-network
    profiles:
      - async

  # MQTT broker for async API testing (optional)
  mosquitto:
    image: eclipse-mosquitto:2.0
    container_name: microcks-mosquitto
    restart: unless-stopped
    ports:
      - "1883:1883"
      - "9001:9001"
    volumes:
      - ./docker/mosquitto/mosquitto.conf:/mosquitto/config/mosquitto.conf:ro
      - mosquitto_data:/mosquitto/data
      - mosquitto_logs:/mosquitto/log
    networks:
      - microcks-network
    profiles:
      - async

  # Prometheus for metrics collection (optional)
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: microcks-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - microcks-network
    profiles:
      - monitoring

  # Grafana for metrics visualization (optional)
  grafana:
    image: grafana/grafana:10.2.0
    container_name: microcks-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./docker/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - microcks-network
    profiles:
      - monitoring

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  zookeeper_data:
    driver: local
  zookeeper_logs:
    driver: local
  kafka_data:
    driver: local
  mosquitto_data:
    driver: local
  mosquitto_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  microcks-network:
    driver: bridge
