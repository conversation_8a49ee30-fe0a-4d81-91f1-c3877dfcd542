{#- Template for "Attributes" sections in docstrings.

This template renders a list of documented attributes in the format
specified with the [`docstring_section_style`][] configuration option.

Context:
  section (griffe.DocstringSectionAttributes): The section to render.
-#}

{% block logs scoped %}
  {#- Logging block.

  This block can be used to log debug messages, deprecation messages, warnings, etc.
  -#}
  {{ log.debug() }}
{% endblock %}

{# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
{% import "language"|get_template as lang with context %}
{#- Language module providing the `t` translation method. -#}

<table class="field-list">
  <colgroup>
    <col class="field-name" />
    <col class="field-body" />
  </colgroup>
  <tbody valign="top">
    <tr class="field">
      <th class="field-name">{{ section.title or lang.t("Attributes:") }}</th>
      <td class="field-body">
        <ul class="first simple">
          {% for attribute in section.value %}
            <li>
              <b><code>{{ attribute.name }}</code></b>
              {% if attribute.annotation %}
                {% with expression = attribute.annotation %}
                  {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
                  (<code>{% include "expression"|get_template with context %}</code>)
                {% endwith %}
              {% endif %}
              –
              <div class="doc-md-description">
                {{ attribute.description|convert_markdown(heading_level, html_id, autoref_hook=autoref_hook) }}
              </div>
            </li>
          {% endfor %}
        </ul>
      </td>
    </tr>
  </tbody>
</table>
