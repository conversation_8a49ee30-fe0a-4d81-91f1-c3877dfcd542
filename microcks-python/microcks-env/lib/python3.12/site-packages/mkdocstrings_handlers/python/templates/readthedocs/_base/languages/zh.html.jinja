<!-- Translations: Chinese -->
{% block logs scoped %}
{% endblock logs %}
{% macro t(key) %}{{ {
  "ATTRIBUTE": "属性",
  "Attributes:": "属性：",
  "Classes:": "类：",
  "CLASS": "类",
  "DEFAULT:": "默认：",
  "Default": "默认",
  "default:": "默认：",
  "DESCRIPTION": "描述",
  "Description": "描述",
  "Examples:": "示例：",
  "Functions:": "函数：",
  "FUNCTION": "函数",
  "Methods:": "方法：",
  "METHOD": "方法",
  "Modules:": "模块：",
  "MODULE": "模块",
  "Name": "名称",
  "Other Parameters:": "其他参数：",
  "PARAMETER": "参数",
  "Parameters:": "参数：",
  "RAISES": "引发",
  "Raises:" : "引发：",
  "Receives:": "接收：",
  "RECEIVES": "接收",
  "required": "必需",
  "RETURNS": "返回",
  "Returns:": "返回：",
  "Source code in": "源代码位于：",
  "TYPE:": "类型：",
  "Type": "类型",
  "Warns:": "警告：",
  "WARNS": "警告",
  "YIELDS": "产生",
  "Yields:": "产生：",
}[key] }}{% endmacro %}