{#- Import translation macros for the given language and fallback language. -#}

{% block logs scoped %}
  {#- Logging block.

  This block can be used to log debug messages, deprecation messages, warnings, etc.
  -#}
{% endblock logs %}

{# YORE: Bump 2: Replace `| get_template` with `~ ".html.jinja"` within line. #}
{% set lang_pth = "languages/" ~ locale | get_template %}
{% if lang_pth is existing_template %}
  {% import lang_pth as lang %}
  {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
  {% import "languages/en"|get_template as fallback %}
  {% macro t(key) %}{{ lang.t(key) or fallback.t(key) }}{% endmacro %}
{% else %}
  {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
  {% import "languages/en"|get_template as lang %}
  {% macro t(key) %}{{ lang.t(key) }}{% endmacro %}
{% endif %}
