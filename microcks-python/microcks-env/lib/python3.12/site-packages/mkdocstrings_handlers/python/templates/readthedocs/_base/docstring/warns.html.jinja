{#- Template for "Warns" sections in docstrings.

This template renders a list of documented warnings in the format
specified with the [`docstring_section_style`][] configuration option.

Context:
  section (griffe.DocstringSectionAttributes): The section to render.
-#}

{% block logs scoped %}
  {#- Logging block.

  This block can be used to log debug messages, deprecation messages, warnings, etc.
  -#}
  {{ log.debug() }}
{% endblock %}

{# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
{% import "language"|get_template as lang with context %}
{#- Language module providing the `t` translation method. -#}

<table class="field-list">
  <colgroup>
    <col class="field-name" />
    <col class="field-body" />
  </colgroup>
  <tbody valign="top">
    <tr class="field">
      <th class="field-name">{{ section.title or lang.t("Warns:") }}</th>
      <td class="field-body">
        <ul class="first simple">
          {% for warns in section.value %}
            <li>
              {% if warns.annotation %}
                {% with expression = warns.annotation, backlink_type = "emitted-by" %}
                  {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
                  <code>{% include "expression"|get_template with context %}</code>
                {% endwith %}
              {% endif %}
              –
              <div class="doc-md-description">
                {{ warns.description|convert_markdown(heading_level, html_id, autoref_hook=autoref_hook) }}
              </div>
            </li>
          {% endfor %}
        </ul>
      </td>
    </tr>
  </tbody>
</table>
