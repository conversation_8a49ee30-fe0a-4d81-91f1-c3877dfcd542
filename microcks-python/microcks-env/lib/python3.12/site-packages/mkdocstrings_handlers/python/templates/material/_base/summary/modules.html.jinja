{#- Summary of modules. -#}

{% block logs scoped %}
  {#- Logging block.

  This block can be used to log debug messages, deprecation messages, warnings, etc.
  -#}
{% endblock logs %}

{% if not obj.docstring.parsed | selectattr("kind.value", "eq", "modules") | list %}
  {% with section = obj.modules
      |filter_objects(
        filters=config.filters,
        members_list=members_list,
        inherited_members=config.inherited_members,
        keep_no_docstrings=config.show_if_no_docstring,
      )
      |order_members("alphabetical", members_list)
      |as_modules_section(check_public=not members_list)
    %}
    {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
    {% if section %}{% include "docstring/modules"|get_template with context %}{% endif %}
  {% endwith %}
{% endif %}
