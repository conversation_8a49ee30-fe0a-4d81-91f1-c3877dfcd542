{#- Template for auto-summaries. -#}

{% block logs scoped %}
  {#- Logging block.

  This block can be used to log debug messages, deprecation messages, warnings, etc.
  -#}
{% endblock logs %}

{% with members_list = config.members if root_members else None %}
  {% if config.summary.modules %}
    {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
    {% include "summary/modules"|get_template with context %}
  {% endif %}

  {% if config.summary.type_aliases %}
    {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
    {% include "summary/type_aliases"|get_template with context %}
  {% endif %}

  {% if config.summary.classes %}
    {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
    {% include "summary/classes"|get_template with context %}
  {% endif %}

  {% if config.summary.functions %}
    {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
    {% include "summary/functions"|get_template with context %}
  {% endif %}

  {% if config.summary.attributes %}
    {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
    {% include "summary/attributes"|get_template with context %}
  {% endif %}
{% endwith %}
