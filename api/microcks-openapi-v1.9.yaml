---
openapi: 3.0.2
info:
  title: Microcks API v1.9
  version: 1.9.0
  description: "API offered by Microcks, the Kubernetes native tool for API and microservices\
    \ mocking and testing (microcks.io)"
  contact:
    name: <PERSON>
    url: https://github.com/microcks
    email: <EMAIL>
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  x-logo:
    backgroundColor: '#ffffff'
    url: https://microcks.io/images/microcks-logo-blue.png
servers:
- url: http://microcks.example.com/api
  description: ""
paths:
  /services:
    summary: This path operations deal with Services
    get:
      tags:
      - mock
      parameters:
      - name: page
        description: Page of Services to retrieve (starts at and defaults to 0)
        schema:
          type: integer
        in: query
      - name: size
        description: Size of a page. Maximum number of Services to include in a response
          (defaults to 20)
        schema:
          type: integer
        in: query
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Service'
          description: List of found Services
      security:
      - jwt-bearer:
        - user
      operationId: GetServices
      summary: Get Services and APIs
  /tests:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestRequest'
        required: true
      tags:
      - test
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestResult'
          description: Created TestResult (empty shell cause tests are executed asynchronously)
      security:
      - jwt-bearer:
        - user
      operationId: CreateTest
      summary: Create a new Test
  /services/count:
    get:
      tags:
      - mock
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Counter'
          description: Number of Services in datastore
      security:
      - jwt-bearer:
        - user
      operationId: GetServicesCounter
      summary: Get the Services counter
  /jobs:
    summary: This path operations deal with ImportJobs
    get:
      tags:
      - job
      parameters:
      - name: page
        description: Page of ImportJobs to retrieve (starts at and defaults to 0)
        schema:
          type: integer
        in: query
      - name: size
        description: Size of a page. Maximum number of ImportJobs to include in a
          response (defaults to 20)
        schema:
          type: integer
        in: query
      - name: name
        description: Name like criterion for query
        schema:
          type: string
        in: query
      responses:
        "200":
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ImportJob'
          description: List of found ImportJobs
      security:
      - jwt-bearer:
        - user
      operationId: GetImportJobs
      summary: Get ImportJobs
      description: Retrieve a list of ImportJobs
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ImportJob'
        required: true
      tags:
      - job
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImportJob'
          description: Created ImportJob
      security:
      - jwt-bearer:
        - user
      operationId: CreateImportJob
      summary: Create ImportJob
      description: Create a new ImportJob
  /jobs/{id}:
    summary: This path or subpaths operations deal with specific ImportJob having
      given id
    get:
      tags:
      - job
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImportJob'
          description: Found ImportJob
      security:
      - jwt-bearer:
        - user
      summary: Get ImportJob
      description: Retrieve an ImportJob using its identifier
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ImportJob'
        required: true
      tags:
      - job
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImportJob'
          description: Updated ImportJob
      summary: Update ImportJob
      description: Update an ImportJob
    delete:
      tags:
      - job
      responses:
        "200":
          content:
            application/json: {}
          description: ImportJob deleted
      security:
      - jwt-bearer:
        - admin
      operationId: DeleteImportJob
      summary: Delete ImportJob
      description: Delete an ImportJob
    parameters:
    - name: id
      description: Unique identifier of ImportJob to manage
      schema:
        type: string
      in: path
      required: true
  /services/{id}:
    get:
      tags:
      - mock
      parameters:
      - name: messages
        description: Whether to include details on services messages into result.
          Default is false
        schema:
          type: boolean
        in: query
      responses:
        "200":
          $ref: '#/components/responses/ServiceResponse'
      security:
      - jwt-bearer:
        - user
      operationId: GetService
      summary: Get Service
    delete:
      tags:
      - mock
      responses:
        "200":
          description: Service has been deleted
      security:
      - jwt-bearer:
        - admin
        - manager
      operationId: DeleteService
      summary: Delete Service
      description: Delete a Service
    parameters:
    - name: id
      description: Unique identifier of Service to managed
      schema:
        type: string
      in: path
      required: true
  /artifact/upload:
    summary: Deals with artifacts to be imported by Microcks.
    post:
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ArtifactUpload'
            examples:
              Artifact upload:
                value: |
                  POST /api/artifact/upload HTTP/1.1
                  Host: microcks
                  User-Agent: curl/7.54.0
                  Accept: */*
                  Authorization: Bearer <REDACTED>
                  Content-Length: 2743
                  Expect: 100-continue
                  Content-Type: multipart/form-data; boundary=------------------------8af8cbb56dd4bde0

                  --------------------------8af8cbb56dd4bde0
                  Content-Disposition: form-data; name="file"; filename="github.json"
                  Content-Type: application/octet-stream

                  THE ARTIFACT HERE

                  --------------------------8af8cbb56dd4bde0--
        required: true
      tags:
      - job
      parameters:
      - name: mainArtifact
        description: Flag telling if this should be considered as primary or secondary
          artifact. Default to 'true'
        schema:
          type: boolean
        in: query
        required: true
      responses:
        "201":
          content:
            text/plain:
              schema:
                type: string
          description: Artifact was imported and Service found
        "204":
          description: No file attribute found in uploaded data
        "400":
          content:
            text/plain:
              schema:
                type: string
          description: Artifact content is invalid and not understood
      security:
      - jwt-bearer:
        - user
      operationId: uploadArtifact
      summary: Upload an artifact
      description: Uploads an artifact to be imported by Microcks.
  /jobs/count:
    summary: Count ImportJobs
    get:
      tags:
      - job
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Counter'
          description: Number of ImportJobs in datastore
      security:
      - jwt-bearer:
        - user
      operationId: GetImportJobCounter
      summary: Get the ImportJobs counter
  /secrets:
    summary: This path operations deal with Secrets
    get:
      tags:
      - config
      parameters:
      - name: page
        description: Page of Secrets to retrieve (starts at and defaults to 0)
        schema:
          type: integer
        in: query
        required: false
      - name: size
        description: Size of a page. Maximum number of Secrets to include in a response
          (defaults to 20)
        schema:
          type: integer
        in: query
        required: false
      responses:
        "200":
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Secret'
          description: List of found Secrets
      security:
      - jwt-bearer:
        - user
      operationId: GetSecrets
      summary: Get Secrets
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Secret'
      tags:
      - config
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Secret'
          description: Created Secret
      security:
      - jwt-bearer:
        - admin
      operationId: CreateSecret
      summary: Create a new Secret
  /secrets/{id}:
    get:
      tags:
      - config
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Secret'
          description: Found Secret
      security:
      - jwt-bearer:
        - admin
      operationId: GetSecret
      summary: Get Secret
      description: Retrieve a Secret
    put:
      tags:
      - config
      responses:
        "200":
          description: Updated Secret
      security:
      - jwt-bearer:
        - admin
      operationId: UpdateSecret
      summary: Update Secret
      description: Update a Secret
    delete:
      tags:
      - config
      responses:
        "200":
          description: Secret has been deleted
      security:
      - jwt-bearer:
        - admin
      operationId: DeleteSecret
      summary: Delete Secret
      description: Delete a Secret
    parameters:
    - name: id
      description: Unique identifier of Secret to manage
      schema:
        type: string
      in: path
      required: true
  /secrets/count:
    get:
      tags:
      - config
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Counter'
          description: Number of Secrets in datastore
      security:
      - jwt-bearer:
        - user
      operationId: GetSecretsCounter
      summary: Get the Secrets counter
  /tests/service/{serviceId}:
    get:
      tags:
      - test
      responses:
        "200":
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TestResult'
          description: List of TestResults for the Service having the requested id
      security:
      - jwt-bearer:
        - user
      operationId: GetTestResultsByService
      summary: Get TestResults by Service
    parameters:
    - name: serviceId
      description: Unique identifier of Service to manage TestResults for
      schema:
        type: string
      in: path
      required: true
  /tests/service/{serviceId}/count:
    get:
      tags:
      - test
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Counter'
          description: Number of TestResults for this Service in datastore
      security:
      - jwt-bearer:
        - user
      operationId: GetTestResultsByServiceCounter
      summary: Get the TestResults for Service counter
    parameters:
    - name: serviceId
      description: Unique identifier of Service to manage TestResults for
      schema:
        type: string
      in: path
      required: true
  /tests/{id}:
    get:
      tags:
      - test
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestResult'
          description: Requested TestResult
      security:
      - jwt-bearer:
        - user
      operationId: GetTestResult
      summary: Get TestResult
      description: ""
    parameters:
    - name: id
      description: Unique identifier of TestResult to manage
      schema:
        type: string
      in: path
      required: true
  /tests/{id}/messages/{testCaseId}:
    get:
      tags:
      - test
      responses:
        "200":
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RequestResponsePair'
          description: List of request and response messages for this TestCase
      security:
      - jwt-bearer:
        - user
      operationId: GetMessagesByTestCase
      summary: Get messages for TestCase
    parameters:
    - name: id
      description: Unique identifier of TestResult to manage
      schema:
        type: string
      in: path
      required: true
    - name: testCaseId
      description: Unique identifier of TetsCaseResult to manage
      schema:
        type: string
      in: path
      required: true
  /tests/{id}/testCaseResult:
    post:
      requestBody:
        description: TestCase return wrapper object
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestCaseReturnDTO'
        required: true
      tags:
      - test
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestCaseResult'
          description: TestCaseResult is reported
      operationId: ReportTestCaseResult
      summary: Report and create a new TestCaseResult
      description: Report a TestCaseResult (typically used by a Test runner)
    parameters:
    - name: id
      description: Unique identifier of TestResult to manage
      schema:
        type: string
      in: path
      required: true
  /keycloak/config:
    summary: Keycloak Authentification configuration
    get:
      tags:
      - config
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KeycloakConfig'
          description: Get current configuration
      operationId: GetKeycloakConfig
      summary: Get authentification configuration
  /services/{id}/operation:
    put:
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OperationOverrideDTO'
        required: true
      tags:
      - mock
      parameters:
      - name: operationName
        description: Name of operation to update
        schema:
          type: string
        in: query
        required: true
      responses:
        "200":
          description: Operation has been updated
        "500":
          description: Operation cannot be updated
      security:
      - jwt-bearer:
        - admin
        - manager
      operationId: OverrideServiceOperation
      summary: Override Service Operation
    parameters:
    - name: id
      description: Unique identifier of Service to managed
      schema:
        type: string
      in: path
      required: true
  /services/{id}/metadata:
    put:
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Metadata'
        required: true
      tags:
      - mock
      responses:
        "200":
          description: Service metadata has been updated
        "500":
          description: Update of metadata failed
      security:
      - jwt-bearer:
        - admin
        - manager
      operationId: UpdateServiceMetadata
      summary: Update Service Metadata
    parameters:
    - name: id
      description: Unique identifier of Service to managed
      schema:
        type: string
      in: path
      required: true
  /services/search:
    get:
      tags:
      - mock
      parameters:
      - name: queryMap
        description: Map of criterion. Key can be simply 'name' with value as the
          searched string. You can also search by label using keys like 'labels.x'
          where 'x' is the label and value the label value
        schema:
          type: object
          additionalProperties:
            type: string
        in: query
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Service'
          description: List of found Services (filtered according search criteria)
      security:
      - jwt-bearer:
        - user
      operationId: SearchServices
      summary: Search for Services and APIs
  /tests/{id}/events/{testCaseId}:
    get:
      tags:
      - test
      responses:
        "200":
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UnidirectionalEvent'
          description: List of event messages for this TestCase
      operationId: GetEventsByTestCase
      summary: Get events for TestCase
    parameters:
    - name: id
      description: Unique identifier of TestResult to manage
      schema:
        type: string
      in: path
      required: true
    - name: testCaseId
      description: Unique identifier of TetsCaseResult to manage
      schema:
        type: string
      in: path
      required: true
  /resources/{name}:
    get:
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Resource'
          description: Retrieve the resource having this name
      security:
      - jwt-bearer:
        - user
      operationId: GetResource
      summary: Get Resource
    parameters:
    - name: name
      description: Unique name/business identifier of the Service or API resource
      schema:
        type: string
      in: path
      required: true
  /resources/service/{serviceId}:
    get:
      responses:
        "200":
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Resource'
          description: List the resources attached to a Service or API
      security:
      - jwt-bearer:
        - user
      operationId: GetResourcesByService
      summary: Get Resources by Service
    parameters:
    - name: serviceId
      description: Unique identifier of the Service or API the resources are attached
        to
      schema:
        type: string
      in: path
      required: true
  /features/config:
    summary: Optional features configuration
    get:
      tags:
      - config
      responses:
        "200":
          content:
            application/json: {}
          description: Get features configuration
      operationId: GetFeaturesConfiguration
      summary: Get features configuration
  /import:
    summary: Deals with repository snapshot to import in Microcks
    post:
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/SnapshotUpload'
        required: true
      tags:
      - mock
      responses:
        "201":
          description: Snasphot has been correctly imported
      security:
      - jwt-bearer:
        - admin
      operationId: importSnapshot
      summary: Import a snapshot
      description: Import a repository snapshot previsouly exported into Microcks
  /export:
    summary: Deals with repository snapshot to import from Microcks
    get:
      tags:
      - mock
      parameters:
      - name: serviceIds
        description: List of service identifiers to export
        schema:
          type: array
          items:
            type: string
        in: query
        required: true
      responses:
        "200":
          headers:
            Content-Disposition:
              schema:
                type: string
              examples:
                filename:
                  value: attachment; filename=microcks-repository.json
          content:
            application/json:
              schema:
                format: binary
                type: string
          description: Snapshot file representing the export of requested services
      security:
      - jwt-bearer:
        - admin
      operationId: exportSnapshot
      summary: Export a snapshot
      description: Export a repostiory snapshot with requested services
  /metrics/invocations/global:
    summary: Invocation Statistics across Services and APIs
    get:
      tags:
      - metrics
      parameters:
      - name: day
        description: The day to get statistics for (formatted with yyyyMMdd pattern).
          Default to today if not provided.
        schema:
          type: string
        in: query
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DailyInvocationStatistic'
          description: Aggregated invocation statistics for specified day
      operationId: GetAggregatedInvocationsStats
      summary: Get aggregated invocation statistics for a day
  /metrics/conformance/aggregate:
    summary: Aggregation of Test conformance metrics
    get:
      tags:
      - metrics
      responses:
        "200":
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WeightedMetricValue'
          description: Get aggregated coverage metric value
      operationId: GetConformanceMetricsAggregation
      summary: Get aggregation of conformance metrics
  /metrics/conformance/service/{serviceId}:
    summary: Test Conformance metrics on API and Services
    get:
      tags:
      - metrics
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestConformanceMetric'
          description: Test coverage metric for Service
      operationId: GetServiceTestConformanceMetric
      summary: Get conformance metrics for a Service
    parameters:
    - name: serviceId
      description: Unique Services identifier this metrics are related to
      schema:
        type: string
      in: path
      required: true
  /metrics/invocations/top:
    summary: Top Invocation Statistics across Services and APIs
    get:
      tags:
      - metrics
      parameters:
      - name: day
        description: The day to get statistics for (formatted with yyyyMMdd pattern).
          Default to today if not provided.
        schema:
          type: string
        in: query
      - name: limit
        description: The number of top invoked mocks to return
        schema:
          type: integer
        in: query
      responses:
        "200":
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DailyInvocationStatistic'
          description: Top invocations for a defined day
      operationId: GetTopIvnocationsStatsByDay
      summary: Get top invocation statistics for a day
  /metrics/invocations/{serviceName}/{serviceVersion}:
    summary: Invocation Statistics for API and Services
    get:
      tags:
      - metrics
      parameters:
      - name: day
        description: The day to get statistics for (formatted with yyyyMMdd pattern).
          Default to today if not provided.
        schema:
          type: string
        in: query
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DailyInvocationStatistic'
          description: Invocation statistics for service for specified day
      operationId: GetInvocationStatsByService
      summary: Get invocation statistics for Service
    parameters:
    - name: serviceName
      description: Name of service to get statistics for
      schema:
        type: string
      in: path
      required: true
    - name: serviceVersion
      description: Version of service to get statistics for
      schema:
        type: string
      in: path
      required: true
  /metrics/invocations/global/latest:
    summary: Latest Invocation Statistics across Services and APIs
    get:
      tags:
      - metrics
      parameters:
      - name: limit
        description: Number of days to get back in time. Default is 20.
        schema:
          type: integer
        in: query
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CounterMap'
          description: A map where keys are day (formatted using yyyyMMdd pattern)
            and values are counter of invocations on this day
      operationId: GetLatestAggregatedInvocationsStats
      summary: Get aggregated invocations statistics for latest days
  /metrics/tests/latest:
    summary: Lastest Test results across Services and APIs
    get:
      tags:
      - metrics
      parameters:
      - name: limit
        description: Number of days to consider for test results to return. Default
          is 7 (one week)
        schema:
          type: integer
        in: query
      responses:
        "200":
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TestResultSummary'
          description: Test results summary for specified <limit> last days.
      operationId: GetLatestTestResults
      summary: Get latest tests results
  /jobs/{id}/start:
    put:
      tags:
      - job
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImportJob'
          description: Started ImportJob
      security:
      - jwt-bearer:
        - manager
        - admin
      operationId: StartImportJob
      summary: Start an ImportJob
      description: Starting an ImportJob forces it to immediatly import mock definitions
    parameters:
    - name: id
      description: Unique identifier of ImportJob to manage
      schema:
        type: string
      in: path
      required: true
  /jobs/{id}/stop:
    put:
      tags:
      - job
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImportJob'
          description: Stopped ImportJob
      security:
      - jwt-bearer:
        - manager
        - admin
      operationId: StopImportJob
      summary: Stop an ImportJob
      description: "Stopping an ImportJob desactivate it, so that it won't execute\
        \ at next schedule"
    parameters:
    - name: id
      description: Unique identifier of ImportJob to manage
      schema:
        type: string
      in: path
      required: true
  /jobs/{id}/activate:
    put:
      tags:
      - job
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImportJob'
          description: ImportJob is activated
      security:
      - jwt-bearer:
        - manager
        - admin
      operationId: ActivateImportJob
      summary: Activate an ImportJob
      description: "Make an ImportJob active, so that it is executed"
    parameters:
    - name: id
      description: Unique identifier of ImportJob to manage
      schema:
        type: string
      in: path
      required: true
  /services/labels:
    get:
      tags:
      - mock
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LabelsMap'
          description: "Already used labels: keys are label Keys, values are array\
            \ of label Values"
      security:
      - jwt-bearer:
        - user
      operationId: GetServicesLabels
      summary: Get the already used labels for Services
components:
  schemas:
    TestCaseResult:
      description: Companion objects for TestResult. Each TestCaseResult correspond
        to a particuliar service operation / action reference by the operationName
        field. TestCaseResults owns a collection of TestStepResults (one for every
        request associated to service operation / action).
      required:
      - success
      - elapsedTime
      - operationName
      properties:
        success:
          description: Flag telling if test case is a success
          type: boolean
        elapsedTime:
          description: Elapsed time in milliseconds since the test case beginning
          type: number
        operationName:
          description: Name of operation this test case is bound to
          type: string
        testStepResults:
          description: Test steps associated to this test case
          type: array
          items:
            $ref: '#/components/schemas/TestStepResult'
    ServiceRef:
      description: Lightweight reference of an existing Service
      required:
      - serviceId
      - name
      - version
      properties:
        serviceId:
          description: Unique reference of a Service
          type: string
        name:
          description: The Service name
          type: string
        version:
          description: The Service version
          type: string
    SecretRef:
      description: Lightweight reference for an existing Secret
      required:
      - secretId
      - name
      properties:
        secretId:
          description: Unique identifier or referenced Secret
          type: string
        name:
          description: Distinct name of the referenced Secret
          type: string
      example: |-
        {
            "secretId": "5be58fb51ed744d1b87481bd",
            "name": "Gogs internal"
        }
    Secret:
      description: A Secret allows grouping informations on how to access a restricted
        resource such as a repsoitory URL. Secrets are typically used by ImpoortJobs.
      required:
      - name
      - description
      properties:
        id:
          description: Unique identifier of Secret
          type: string
        name:
          description: Unique distinct name of Secret
          type: string
        username:
          type: string
        password:
          type: string
        token:
          type: string
        tokenHeader:
          type: string
        caCertPem:
          type: string
        description:
          description: Description of this Secret
          type: string
      example: |-
        {
            "id": "5be58fb51ed744d1b87481bd",
            "name": "Gogs internal",
            "description": "Gogs internal corporate repository",
            "username": "team",
            "password": "team",
            "caCertPem": "-----BEGIN CERTIFICATE-----\nMIIC6jCCAdKgAwIBAgIBATANBgkqhkiG9w0BAQsFADAmMSQwIgYDVQQDDBtvcGVu\nc2hpZnQtc2lnbmVyQDE1MzE5MTA5MDIwHhcNMTgwNzE4MTA0ODIyWhcNMjMwNzE3\nMTA0ODIzWjAmMSQwIgYDVQQDDBtvcGVuc2hpZnQtc2lnbmVyQDE1MzE5MTA5MDIw\nggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCyaP1jlpnm8WpfCSnUa8Qt\nhdUynOLLgElUtpWoW25wB9tO2ZmEj+fVsTyzEsW8+nfXXfRsBEzPm2ze9uEMTPTB\nAY0k7DbLZfjmF1lCckUvvh1rR/8hoPuXETjXUuOdtm7gRHTOxLQyH2Qi/q0DYJAn\nprKyRCLa35pRnykL6v4bHkqFnqDEho63i29XHhm2703moh4YCl1iYa2Rh6D44cjn\n8lBCq6o7zoZSmc/aBamRkQrfZYcolR8OUtDS4oEB0zMftmea2ycashsLEMB+Cq4r\n64NI2QM7qOxdTuXsDivHfLl7RTuWEOozGaJXoiPaGU/3d/KnY0gKJ2TC1KXt6Xjn\nAgMBAAGjIzAhMA4GA1UdDwEB/wQEAwICpDAPBgNVHRMBAf8EBTADAQH/MA0GCSqG\nSIb3DQEBCwUAA4IBAQCeUmxfUzw0VAFG6HvVYIsfvumiIvsSWmclGIZqNJyfMHFD\nMy6xzPRNNfWe5aumGTJsuIzuXfDRcdO7KmH1d2/5brkvWpxp6svVrYPvcoXjl4VN\nQR2mv5Di4KHfiiwvP3eeewjUZj+uREGqX2fcbJPHTPy32Kpb0H8Uy09BklhjC7QP\ngRAGexPhU1oBL/CoOwbHKcQ6dxs/y1SxzI8gXEtec4z62CroI13iT7U0UjSqFBE4\nKfrJombfz0d68781Z40ll+8my251ZNfbLBhQ3UHW0JnkBEQkE1aBorUoj2iakYvx\nA2qZh+8q2b8MwMb2YsQ0dlxKd6c4tN3lmMnO4bnd\n-----END CERTIFICATE-----"
        }
    TestCaseReturnDTO:
      required:
      - operationName
      properties:
        operationName:
          description: Name of related operation for this TestCase
          type: string
    TestReturn:
      description: TestReturn is used for wrapping the return code of a test step
        execution
      required:
      - code
      - elapsedTime
      properties:
        code:
          description: "Return code for test (0 means Success, 1 means Failure)"
          type: integer
        elapsedTime:
          format: int64
          description: Elapsed time in milliseconds
          type: integer
        message:
          description: Error message if any
          type: string
        request:
          $ref: '#/components/schemas/Request'
          description: Request sent for this test
        response:
          $ref: '#/components/schemas/Response'
          description: Response returned for this test
        eventMessage:
          $ref: '#/components/schemas/EventMessage'
          description: Event Message received for this test
    Request:
      description: A mock invocation or test request
      required:
      - name
      - operationId
      properties:
        id:
          description: Unique identifier of Request
          type: string
        name:
          description: Unique distinct name of this Request
          type: string
        content:
          description: Body content for this request
          type: string
        operationId:
          description: Identifier of Operation this Request is associated to
          type: string
        testCaseId:
          description: Unique identifier of TestCase this Request is attached (in
            case of a test)
          type: string
        headers:
          description: Headers for this Request
          type: array
          items:
            $ref: '#/components/schemas/Header'
    Response:
      description: A mock invocation or test response
      required:
      - operationId
      - name
      properties:
        operationId:
          description: Identifier of Operation this Response is associated to
          type: string
        content:
          description: Body content of this Response
          type: string
        id:
          description: Unique identifier of Response
          type: string
        name:
          description: Unique distinct name of this Response
          type: string
        testCaseId:
          description: Unique identifier of TestCase this Response is attached (in
            case of a test)
          type: string
        headers:
          description: Headers for this Response
          type: array
          items:
            $ref: '#/components/schemas/Header'
    Header:
      description: Transport headers for both Requests and Responses
      required:
      - name
      - values
      type: object
      properties:
        name:
          description: Unique distinct name of this Header
          type: string
        values:
          description: Values for this Header
          type: array
          items:
            type: string
    TestStepResult:
      description: TestStepResult is an entity embedded within TestCaseResult. They
        are created for each request associated with an operation / action of a microservice.
      required:
      - success
      - elapsedTome
      properties:
        success:
          description: Flag telling if test case is a success
          type: boolean
        elapsedTime:
          description: Elapsed time in milliseconds since the test step beginning
          type: number
        requestName:
          description: Name of request this test step is bound to
          type: string
        message:
          description: Error message that may be associated to this test step
          type: string
        eventMessageName:
          description: Name of event this test step is bound to
          type: string
    KeycloakConfig:
      description: Representation of Keycloak / SSO configuration used by Microcks
        server
      required:
      - realm
      - auth-server-url
      - public-client
      - ssl-required
      - resource
      - enabled
      type: object
      properties:
        realm:
          description: Authentication realm name
          type: string
        auth-server-url:
          description: SSO Server authentication url
          type: string
        public-client:
          description: Name of public-client that can be used for requesting OAuth
            token
          type: boolean
        ssl-required:
          description: SSL certificates requirements
          enum:
          - none
          - external
        resource:
          description: Name of Keycloak resource/application used on client side
          type: string
        enabled:
          description: Whether Keycloak authentification and usage is enabled
          type: boolean
    RequestResponsePair:
      description: Request associated with corresponding Response
      type: object
      allOf:
      - required:
        - request
        - response
        type: object
        properties:
          request:
            $ref: '#/components/schemas/Request'
            description: The request part of the pair
          response:
            $ref: '#/components/schemas/Response'
            description: The Response part of the pair
      - $ref: '#/components/schemas/AbstractExchange'
    OperationOverrideDTO:
      description: Data Transfer object for grouping the mutable properties of an
        Operation
      type: object
      properties:
        dispatcher:
          description: Type of dispatcher to apply for this operation
          type: string
        dispatcherRules:
          description: Rules of dispatcher for this operation
          type: string
        defaultDelay:
          description: Default delay in milliseconds to apply to mock responses on
            this operation
          type: integer
        parameterConstraints:
          description: Constraints that may apply to incoming parameters on this operation
          type: array
          items:
            $ref: '#/components/schemas/ParameterConstraint'
    ParameterConstraint:
      description: Companion object for Operation that may be used to express constraints
        on request parameters
      required:
      - name
      type: object
      properties:
        name:
          description: Parameter name
          type: string
        required:
          description: Whether it's a required constraint
          type: boolean
        recopy:
          description: Whether it's a recopy constraint
          type: boolean
        mustMatchRegexp:
          description: Whether it's a regular expression matching constraint
          type: string
        in:
          description: Parameter location
          enum:
          - path
          - query
          - header
          type: string
    Metadata:
      description: Commodity object for holding metadata on any entity. This object
        is inspired by Kubernetes metadata.
      required:
      - createdOn
      - lastUpdate
      type: object
      properties:
        createdOn:
          description: Creation date of attached object
          type: number
          readOnly: true
        lastUpdate:
          description: Last update of attached object
          type: number
          readOnly: true
        annotations:
          description: Annotations of attached object
          type: object
          additionalProperties:
            type: string
        labels:
          description: Labels put on attached object
          type: object
          additionalProperties:
            type: string
    LabelsMap:
      description: A map which keys are already used labels keys and values are already
        used values for this key
      type: object
      additionalProperties:
        $ref: '#/components/schemas/StringArray'
        type: object
    UnidirectionalEvent:
      description: Representation of an unidirectional exchange as an event message
      type: object
      allOf:
      - required:
        - eventMessage
        type: object
        properties:
          eventMessage:
            $ref: '#/components/schemas/EventMessage'
            description: Asynchronous message for this unidirectional event
      - $ref: '#/components/schemas/AbstractExchange'
    EventMessage:
      description: ""
      required:
      - id
      - mediaType
      type: object
      properties:
        id:
          description: Unique identifier of this message
          type: string
        mediaType:
          description: Content type of message
          type: string
        name:
          description: Unique distinct name of this message
          type: string
        content:
          description: Body content for this message
          type: string
        operationId:
          description: Identifier of Operation this message is associated to
          type: string
        testCaseId:
          description: Unique identifier of TestCase this message is attached (in
            case of a test)
          type: string
        headers:
          description: Headers for this message
          type: array
          items:
            $ref: '#/components/schemas/Header'
    StringArray:
      description: A simple array of String
      type: array
      items:
        type: string
    MessageArray:
      description: Array of Message for Service operations
      type: array
      items:
        $ref: '#/components/schemas/Exchange'
    Exchange:
      oneOf:
      - $ref: '#/components/schemas/RequestResponsePair'
      - $ref: '#/components/schemas/UnidirectionalEvent'
      discriminator:
        propertyName: type
        mapping:
          reqRespPair: '#/components/schemas/RequestResponsePair'
          unidirEvent: '#/components/schemas/UnidirectionalEvent'
      description: "Abstract representation of a Service or API exchange type (request/response,\
        \ event based, ...)"
    AbstractExchange:
      description: Abstract bean representing a Service or API Exchange.
      required:
      - type
      type: object
      properties:
        type:
          description: Discriminant type for identifying kind of exchange
          enum:
          - reqRespPair
          - unidirEvent
          type: string
    ServiceView:
      description: Aggregate bean for grouping a Service an its messages pairs
      required:
      - service
      - messagesMap
      type: object
      properties:
        service:
          $ref: '#/components/schemas/Service'
          description: Wrapped service description
        messagesMap:
          description: "Map of messages for this Service. Keys are operation name,\
            \ values are array of messages for this operation"
          type: object
          additionalProperties:
            $ref: '#/components/schemas/MessageArray'
    TestRequest:
      description: Test request is a minimalist wrapper for requesting the launch
        of a new test
      required:
      - serviceId
      - testEndpoint
      - runnerType
      - timeout
      properties:
        serviceId:
          description: Unique identifier of service to test
          type: string
        testEndpoint:
          description: Endpoint to test for this service
          type: string
        runnerType:
          $ref: '#/components/schemas/TestRunnerType'
          description: Runner used for this test
        timeout:
          description: The maximum time (in milliseconds) to wait for this test ends
          type: integer
        filteredOperations:
          description: A restriction on service operations to test
          type: array
          items:
            type: string
        secretName:
          description: The name of Secret to use for connecting the test endpoint
          type: string
        oAuth2Context:
          $ref: '#/components/schemas/OAuth2ClientContent'
          description: An OAuth2 context to use for retrieving an access token prior
            invoking the tested endpoint
        operationsHeaders:
          $ref: '#/components/schemas/OperationHeaders'
          description: This test operations headers override
    Resource:
      description: "Resource represents a Service or API artifacts such as specification,\
        \ contract"
      required:
      - id
      - name
      - content
      - type
      - serviceId
      type: object
      properties:
        id:
          description: Uniquer identifier of this Service or API Resource
          type: string
        name:
          description: Unique name/business identifier for this Service or API resource
          type: string
        content:
          description: String content of this resource
          type: string
        type:
          $ref: '#/components/schemas/ResourceType'
          description: Type of this Service or API resource
        serviceId:
          description: Unique identifier of the Servoce or API this resource is attached
            to
          type: string
        path:
          description: Relative path of this resource regarding main resource
          type: string
        sourceArtifact:
          description: Short name of the artifact this resource was extracted from
          type: string
    ImportJob:
      description: An ImportJob allow defining a repository artifact to poll for discovering
        Services and APIs mocks and tests
      required:
      - name
      - repositoryUrl
      properties:
        id:
          description: Unique identifier of ImportJob
          type: string
        name:
          description: Unique distinct name of this ImportJob
          type: string
        repositoryUrl:
          description: URL of mocks and tests repository artifact
          type: string
        repositoryDisableSSLValidation:
          description: Whether to disable SSL certificate verification when checking
            repository
          type: boolean
        frequency:
          description: Reserved for future usage
          type: string
        createdDate:
          format: date-time
          description: Creation date for this ImportJob
          type: string
        lastImportDate:
          format: date-time
          description: Date last import was done
          type: string
        lastImportError:
          description: Error message of last import (if any)
          type: string
        active:
          description: Whether this ImportJob is active (ie. scheduled for execution)
          type: boolean
        etag:
          description: Etag of repository URL during previous import. Is used for
            not re-importing if no recent changes
          type: string
        serviceRefs:
          description: References of Services discovered when checking repository
          type: array
          items:
            $ref: '#/components/schemas/ServiceRef'
        metadata:
          $ref: '#/components/schemas/Metadata'
          description: Metadata of ImportJob
        secretRef:
          $ref: '#/components/schemas/SecretRef'
          description: Reference of a Secret to used when checking repository
        mainArtifact:
          description: Flag telling if considered as primary or secondary artifact.
            Default to `true`
          type: boolean
    TestRunnerType:
      description: Type of test strategy (different strategies are implemented by
        different runners)
      enum:
      - HTTP
      - SOAP_HTTP
      - SOAP_UI
      - POSTMAN
      - OPEN_API_SCHEMA
      - ASYNC_API_SCHEMA
      - GRPC_PROTOBUF
      - GRAPHQL_SCHEMA
      type: string
    Operation:
      description: An Operation of a Service or API
      required:
      - name
      - method
      type: object
      properties:
        name:
          description: Unique name of this Operation within Service scope
          type: string
        method:
          description: Represents transport method
          type: string
        inputName:
          description: Name of input parameters in case of Xml based Service
          type: string
        outputName:
          description: Name of output parameters in case of Xml based Service
          type: string
        dispatcher:
          description: Dispatcher strategy used for mocks
          type: string
        dispatcherRules:
          description: DispatcherRules used for mocks
          type: string
        defaultDelay:
          description: Default response time delay for mocks
          type: number
        resourcePaths:
          description: Paths the mocks endpoints are mapped on
          type: array
          items:
            type: string
        parameterContraints:
          description: Contraints that may apply to mock invocatino on this operation
          type: array
          items:
            $ref: '#/components/schemas/ParameterConstraint'
        bindings:
          description: Map of protocol binding details for this operation
          type: object
          additionalProperties:
            $ref: '#/components/schemas/Binding'
    ArtifactUpload:
      description: |-
        Artifact to be imported by Microcks.
        This structure represents a mime-multipart file upload (as specified here: https://swagger.io/docs/specification/describing-request-body/file-upload/)
      required:
      - file
      type: object
      properties:
        file:
          format: binary
          description: The artifact to upload
          type: string
    Counter:
      description: A simple Counter type.
      type: object
      properties:
        counter:
          format: int32
          description: Number of items in a resource collection
          type: integer
      example: |-
        {
            "counter": 12
        }
    FeaturesConfig:
      title: Root Type for FeaturesConfig
      description: Representation of optional features configuration used by Microcks
        server
      type: object
      properties:
        repository-filter:
          description: Repository filtering feature properties
          type: object
          properties:
            label-label:
              type: string
            enabled:
              type: string
            label-list:
              type: string
            label-key:
              type: string
        microcks-hub:
          description: Microcks Hub feature properties
          type: object
          properties:
            allowed-roles:
              type: string
            endpoint:
              type: string
            enabled:
              type: string
        repository-tenancy:
          description: Repository tenancy feature properties
          type: object
          properties:
            enabled:
              type: string
            artifact-import-allowed-roles:
              type: string
        async-api:
          description: Asynchronous feature properties
          type: object
          properties:
            default-binding:
              type: string
            endpoint-MQTT:
              type: string
            endpoint-WS:
              type: string
            endpoint-KAFKA:
              type: string
            endpoint-AMQP:
              type: string
            endpoint-NATS:
              type: string
            endpoint-GOOGLEPUBSUB:
              type: string
            enabled:
              type: string
            frequencies:
              type: string
      additionalProperties: true
      example:
        repository-filter:
          label-label: Domain
          enabled: "true"
          label-list: "domain,status"
          label-key: domain
        microcks-hub:
          allowed-roles: "admin,manager,manager-any"
          endpoint: https://hub.microcks.io/api
          enabled: "true"
        repository-tenancy:
          enabled: "false"
          artifact-import-allowed-roles: "admin,manager,manager-any"
        async-api:
          default-binding: KAFKA
          endpoint-MQTT: my-mqtt-broker.apps.try.microcks.io
          endpoint-WS: localhost:8081
          endpoint-KAFKA: my-cluster-kafka-bootstrap.apps.try.microcks.io
          enabled: "false"
          frequencies: "3,10,30"
    SnapshotUpload:
      description: Upload of a repository snapshot file
      required:
      - file
      type: object
      properties:
        file:
          format: binary
          description: The repository snapshot file
          type: string
    HeaderDTO:
      description: Data Transfert Object for headers of both Requests and Responses
      required:
      - name
      - values
      type: object
      properties:
        name:
          description: Unique distinct name of this Header
          type: string
        values:
          description: Values for this header (comma separated strings)
          type: string
    OperationHeaders:
      description: "Specification of additional headers for a Service/API operations.\
        \ Keys are operation name or \"globals\" (if header applies to all), values\
        \ are Header objects DTO."
      type: object
      additionalProperties:
        type: array
        items:
          $ref: '#/components/schemas/HeaderDTO'
    Service:
      description: Represents a Service or API definition as registred into Microcks
        repository
      required:
      - name
      - version
      - type
      - sourceArtifact
      properties:
        id:
          description: Unique identifier for this Service or API
          type: string
        name:
          description: Distinct name for this Service or API (maybe shared among many
            versions)
          type: string
        version:
          description: Distinct version for a named Service or API
          type: string
        type:
          description: Service or API Type
          enum:
          - REST
          - SOAP_HTTP
          - GENERIC_REST
          - GENERIC_EVENT
          - EVENT
          - GRPC
          - GRAPHQL
          type: string
        operations:
          description: Set of Operations for Service or API
          type: array
          items:
            $ref: '#/components/schemas/Operation'
        xmlNS:
          description: Associated Xml Namespace in case of Xml based Service
          type: string
        metadata:
          $ref: '#/components/schemas/Metadata'
          description: Metadata of Service
        sourceArtifact:
          description: Short name of the main/primary artifact this service was created
            from
          type: string
    Trend:
      description: Evolution trend qualifier
      enum:
      - DOWN
      - LOW_DOWN
      - STABLE
      - LOW_UP
      - UP
      type: string
    WeightedMetricValue:
      description: Value of a metric with an associated weight
      required:
      - name
      - weight
      - value
      type: object
      properties:
        name:
          description: Metric name or serie name
          type: string
        weight:
          description: Weight of this metric value (typically a percentage)
          type: integer
        value:
          description: The value of this metric
          type: integer
    DailyInvocationStatistic:
      description: The daily statistic of a service mock invocations
      required:
      - id
      - day
      - serviceName
      - serviceVersion
      - dailyCount
      type: object
      properties:
        id:
          description: Unique identifier of this statistic object
          type: string
        day:
          description: The day (formatted as yyyyMMdd string) represented by this
            statistic
          type: string
        serviceName:
          description: The name of the service this statistic is related to
          type: string
        serviceVersion:
          description: The version of the service this statistic is related to
          type: string
        dailyCount:
          description: The number of service mock invocations on this day
          type: number
        hourlyCount:
          description: The number of service mock invocations per hour of the day
            (keys range from 0 to 23)
          type: object
          additionalProperties: true
        minuteCount:
          description: The number of service mock invocations per minute of the day
            (keys range from 0 to 1439)
          type: object
          additionalProperties: true
    TestConformanceMetric:
      description: "Represents the test conformance metrics (current score, history\
        \ and evolution trend) of a Service"
      required:
      - id
      - serviceId
      - currentScore
      - maxPossibleScore
      type: object
      properties:
        id:
          description: Unique identifier of coverage metric
          type: string
        serviceId:
          description: Unique identifier of the Service this metric is related to
          type: string
        aggregationLabelValue:
          description: Value of the label used for metrics aggregation (if any)
          type: string
        maxPossibleScore:
          format: double
          description: Maximum conformance score that can be reached (depends on samples
            expresiveness)
          type: number
        currentScore:
          format: double
          description: Current test conformance score for the related Service
          type: number
        lastUpdateDay:
          description: The day of latest score update (in yyyyMMdd format)
          type: string
        latestTrend:
          $ref: '#/components/schemas/Trend'
          description: Evolution trend of currentScore
        latestScores:
          description: "History of latest scores (key is date with format yyyyMMdd,\
            \ value is score as double)"
          type: object
          additionalProperties:
            type: number
    TestResultSummary:
      description: 'Represents the summary result of a Service or API test run by
        Microcks. '
      required:
      - id
      - testDate
      - serviceId
      - success
      properties:
        id:
          description: Unique identifier of TestResult
          type: string
        testDate:
          format: int64
          description: Timestamp of creation date of this service
          type: integer
        serviceId:
          description: Unique identifier of service tested
          type: string
        success:
          description: Flag telling if test is a success
          type: boolean
    TestResult:
      description: "Represents the result of a Service or API test run by Microcks.\
        \ Tests are related to a service and made of multiple test cases corresponding\
        \ to each operations / actions composing service. Tests are run against a\
        \ specific endpoint named testedEndpoint. It holds global markers telling\
        \ if test still ran, is a success, how many times is has taken and so on ..."
      required:
      - id
      - version
      - testNumber
      - testDate
      - testedEndpoint
      - serviceId
      - success
      - inProgress
      - runnerType
      properties:
        id:
          description: Unique identifier of TestResult
          type: string
        version:
          description: Revision number of this test
          type: number
        testNumber:
          description: Incremental number for tracking number of tests of a service
          type: number
        testDate:
          format: int64
          description: Timestamp of creation date of this service
          type: integer
        testedEndpoint:
          description: Endpoint used during test
          type: string
        serviceId:
          description: Unique identifier of service tested
          type: string
        elapsedTime:
          description: Elapsed time in milliseconds since test beginning
          type: number
        success:
          description: Flag telling if test is a success
          type: boolean
        inProgress:
          description: Flag telling is test is still in progress
          type: boolean
        runnerType:
          $ref: '#/components/schemas/TestRunnerType'
          description: Runner used for this test
        testCaseResults:
          description: TestCase results associated to this test
          type: array
          items:
            $ref: '#/components/schemas/TestCaseResult'
        secretRef:
          $ref: '#/components/schemas/SecretRef'
          description: The referrence of the Secret used for connecting to test endpoint
        operationHeaders:
          $ref: '#/components/schemas/OperationHeaders'
          description: This test operations headers override
        timeout:
          description: The maximum time (in milliseconds) to wait for this test ends
          type: integer
        authorizedClient:
          $ref: '#/components/schemas/OAuth2AuthorizedClient'
          description: The OAuth2 authorized client that performed the test
    Binding:
      description: Protocol binding details for asynchronous operations
      required:
      - type
      - destinationName
      type: object
      properties:
        type:
          description: Protocol binding identifier
          enum:
          - KAFKA
          - MQTT
          - WS
          - AMQP
          - NATS
          - GOOGLEPUBSUB
          type: string
        keyType:
          description: Type of key for Kafka messages
          type: string
        destinationType:
          description: Type of destination for asynchronous messages of this operation
          type: string
        destinationName:
          description: Name of destination for asynchronous messages of this operation
          type: string
        qoS:
          description: Quality of Service attribute for MQTT binding
          type: string
        persistent:
          description: Persistent attribute for MQTT binding
          type: boolean
        method:
          description: HTTP method for WebSocket binding
          type: string
    ResourceType:
      description: Types of managed resources for Services or APIs
      enum:
      - WSDL
      - XSD
      - JSON_SCHEMA
      - OPEN_API_SPEC
      - OPEN_API_SCHEMA
      - ASYNC_API_SPEC
      - ASYNC_API_SCHEMA
      - AVRO_SCHEMA
      - PROTOBUF_SCHEMA
      - PROTOBUF_DESCRIPTION
      - GRAPHQL_SCHEMA
      - POSTMAN_COLLECTION
      type: string
    CounterMap:
      description: A generic map of counter
      type: object
      additionalProperties:
        type: number
    OAuth2ClientContent:
      description: Represents a volatile OAuth2 client context usually associated
        with a Test request
      required:
      - clientId
      - clientSecret
      - tokenUri
      type: object
      properties:
        clientId:
          description: Id for connecting to OAuth2 identity provider
          type: string
        clientSecret:
          format: password
          description: Secret for connecting to OAuth2 identity provider
          type: string
        tokenUri:
          description: URI for retrieving an access token from OAuth2 identity provider
          type: string
        username:
          description: Username in case you're using the Resource Owner Password flow
          type: string
        password:
          description: User password in case you're suing the Resource Owner password
            flow
          type: string
        refreshToken:
          description: Refresh token in case you're using the Refresh Token rotation
            flow
          type: string
    OAuth2GrantType:
      description: Enumeration for the different supported grants/flows of OAuth2
      enum:
      - PASSWORD
      - CLIENT_CREDENTIALS
      - REFRESH_TOKEN
      type: string
    OAuth2AuthorizedClient:
      description: OAuth2 authorized client that performed a test
      required:
      - grantType
      - principalName
      - tokenUri
      type: object
      properties:
        grantType:
          $ref: '#/components/schemas/OAuth2GrantType'
          description: OAuth2 authorization flow/grant type applied
        principalName:
          description: Name of authorized principal (clientId or username in the case
            of Password grant type)
          type: string
        tokenUri:
          description: Identity Provider URI used for token retrieval
          type: string
        scopes:
          description: Included scopes (separated using space)
          type: string
  responses:
    ServiceResponse:
      content:
        application/json:
          schema:
            oneOf:
            - $ref: '#/components/schemas/Service'
            - $ref: '#/components/schemas/ServiceView'
      description: ""
  securitySchemes:
    jwt-bearer:
      flows:
        clientCredentials:
          tokenUrl: https://keycloak.example.com/realms/microcks/protocol/openid-connect/token
          refreshUrl: https://keycloak.example.com/realms/microcks/protocol/openid-connect/token
          scopes:
            user: Simple authenticated user
            manager: Services & APIs content manager
            admin: Administrator of the Microcks instance
      type: oauth2
      description: JWT Bearer acquired using OAuth 2 Authentication flow or Direct
        Access Grant
security:
- jwt-bearer: []
tags:
- name: mock
  description: Operations related to API and Services mocks
- name: test
  description: Operations related to API and Services tests
- name: job
  description: Operations related to Jobs for discovering mocks and tests
- name: config
  description: Operations related to configuration
- name: metrics
  description: Operations related to metrics
